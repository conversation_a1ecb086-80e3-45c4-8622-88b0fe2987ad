{"logs": [{"outputFile": "com.falaileh.elmarjo.app-mergeDebugResources-65:/values-ml/values-ml.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5ae455f1a13db310449e207b49d3bacc\\transformed\\appcompat-1.1.0\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,318,429,520,625,747,825,901,992,1084,1185,1279,1380,1474,1569,1668,1759,1850,1931,2040,2144,2243,2355,2467,2588,2753,2854", "endColumns": "106,105,110,90,104,121,77,75,90,91,100,93,100,93,94,98,90,90,80,108,103,98,111,111,120,164,100,81", "endOffsets": "207,313,424,515,620,742,820,896,987,1079,1180,1274,1375,1469,1564,1663,1754,1845,1926,2035,2139,2238,2350,2462,2583,2748,2849,2931"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,176", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "775,882,988,1099,1190,1295,1417,1495,1571,1662,1754,1855,1949,2050,2144,2239,2338,2429,2520,2601,2710,2814,2913,3025,3137,3258,3423,16093", "endColumns": "106,105,110,90,104,121,77,75,90,91,100,93,100,93,94,98,90,90,80,108,103,98,111,111,120,164,100,81", "endOffsets": "877,983,1094,1185,1290,1412,1490,1566,1657,1749,1850,1944,2045,2139,2234,2333,2424,2515,2596,2705,2809,2908,3020,3132,3253,3418,3519,16170"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\eb5978be55e4593326b90344069d1b2d\\transformed\\ui-release\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,287,386,490,580,666,767,854,942,1028,1115,1193,1270,1344,1417,1493,1560", "endColumns": "94,86,98,103,89,85,100,86,87,85,86,77,76,73,72,75,66,118", "endOffsets": "195,282,381,485,575,661,762,849,937,1023,1110,1188,1265,1339,1412,1488,1555,1674"}, "to": {"startLines": "53,54,56,57,58,111,112,170,171,174,175,177,178,179,180,183,184,185", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4261,4356,4515,4614,4718,8841,8927,15518,15605,15920,16006,16175,16253,16330,16404,16747,16823,16890", "endColumns": "94,86,98,103,89,85,100,86,87,85,86,77,76,73,72,75,66,118", "endOffsets": "4351,4438,4609,4713,4803,8922,9023,15600,15688,16001,16088,16248,16325,16399,16472,16818,16885,17004"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d0e5df3da8374bed71d9993cb807cb2d\\transformed\\core-1.16.0\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,362,466,569,670,792", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "152,255,357,461,564,665,787,888"}, "to": {"startLines": "46,47,48,49,50,51,52,181", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3524,3626,3729,3831,3935,4038,4139,16477", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "3621,3724,3826,3930,4033,4134,4256,16573"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5510098f7f655104979ce51ebf04d4e9\\transformed\\media3-exoplayer-1.2.1\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,187,259,329,406,487,590,687", "endColumns": "70,60,71,69,76,80,102,96,77", "endOffsets": "121,182,254,324,401,482,585,682,760"}, "to": {"startLines": "83,84,85,86,87,88,89,90,91", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6908,6979,7040,7112,7182,7259,7340,7443,7540", "endColumns": "70,60,71,69,76,80,102,96,77", "endOffsets": "6974,7035,7107,7177,7254,7335,7438,7535,7613"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\3109e90bbcb133a6123d6a171e482e61\\transformed\\foundation-release\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,91", "endOffsets": "138,230"}, "to": {"startLines": "186,187", "startColumns": "4,4", "startOffsets": "17009,17097", "endColumns": "87,91", "endOffsets": "17092,17184"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\32ffae0c59a3b7f539124213f72623ec\\transformed\\media3-ui-1.2.1\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,294,521,725,826,926,1017,1110,1219,1296,1363,1455,1547,1624,1695,1756,1830,1950,2072,2191,2270,2351,2423,2500,2596,2691,2760,2825,2878,2936,2986,3047,3113,3175,3236,3306,3367,3431,3497,3568,3635,3691,3753,3829,3905", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,100,99,90,92,108,76,66,91,91,76,70,60,73,119,121,118,78,80,71,76,95,94,68,64,52,57,49,60,65,61,60,69,60,63,65,70,66,55,61,75,75,53", "endOffsets": "289,516,720,821,921,1012,1105,1214,1291,1358,1450,1542,1619,1690,1751,1825,1945,2067,2186,2265,2346,2418,2495,2591,2686,2755,2820,2873,2931,2981,3042,3108,3170,3231,3301,3362,3426,3492,3563,3630,3686,3748,3824,3900,3954"}, "to": {"startLines": "2,11,15,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,344,571,4808,4909,5009,5100,5193,5302,5379,5446,5538,5630,5707,5778,5839,5913,6033,6155,6274,6353,6434,6506,6583,6679,6774,6843,7618,7671,7729,7779,7840,7906,7968,8029,8099,8160,8224,8290,8361,8428,8484,8546,8622,8698", "endLines": "10,14,18,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109", "endColumns": "17,12,12,100,99,90,92,108,76,66,91,91,76,70,60,73,119,121,118,78,80,71,76,95,94,68,64,52,57,49,60,65,61,60,69,60,63,65,70,66,55,61,75,75,53", "endOffsets": "339,566,770,4904,5004,5095,5188,5297,5374,5441,5533,5625,5702,5773,5834,5908,6028,6150,6269,6348,6429,6501,6578,6674,6769,6838,6903,7666,7724,7774,7835,7901,7963,8024,8094,8155,8219,8285,8356,8423,8479,8541,8617,8693,8747"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d5341083af57bd2b42f46054ffafb559\\transformed\\preference-1.2.1\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,177,266,352,493,662,744", "endColumns": "71,88,85,140,168,81,75", "endOffsets": "172,261,347,488,657,739,815"}, "to": {"startLines": "55,110,172,173,182,188,189", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4443,8752,15693,15779,16578,17189,17271", "endColumns": "71,88,85,140,168,81,75", "endOffsets": "4510,8836,15774,15915,16742,17266,17342"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e754f461ad8f7a511fa7fc6c4385c693\\transformed\\material3-release\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,301,427,550,650,744,855,1007,1125,1282,1367,1472,1572,1674,1797,1930,2040,2176,2318,2449,2653,2787,2911,3041,3175,3276,3374,3492,3623,3722,3824,3937,4075,4221,4335,4444,4520,4618,4718,4832,4919,5016,5124,5204,5292,5390,5503,5598,5709,5799,5914,6016,6129,6261,6341,6448", "endColumns": "119,125,125,122,99,93,110,151,117,156,84,104,99,101,122,132,109,135,141,130,203,133,123,129,133,100,97,117,130,98,101,112,137,145,113,108,75,97,99,113,86,96,107,79,87,97,112,94,110,89,114,101,112,131,79,106,96", "endOffsets": "170,296,422,545,645,739,850,1002,1120,1277,1362,1467,1567,1669,1792,1925,2035,2171,2313,2444,2648,2782,2906,3036,3170,3271,3369,3487,3618,3717,3819,3932,4070,4216,4330,4439,4515,4613,4713,4827,4914,5011,5119,5199,5287,5385,5498,5593,5704,5794,5909,6011,6124,6256,6336,6443,6540"}, "to": {"startLines": "113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9028,9148,9274,9400,9523,9623,9717,9828,9980,10098,10255,10340,10445,10545,10647,10770,10903,11013,11149,11291,11422,11626,11760,11884,12014,12148,12249,12347,12465,12596,12695,12797,12910,13048,13194,13308,13417,13493,13591,13691,13805,13892,13989,14097,14177,14265,14363,14476,14571,14682,14772,14887,14989,15102,15234,15314,15421", "endColumns": "119,125,125,122,99,93,110,151,117,156,84,104,99,101,122,132,109,135,141,130,203,133,123,129,133,100,97,117,130,98,101,112,137,145,113,108,75,97,99,113,86,96,107,79,87,97,112,94,110,89,114,101,112,131,79,106,96", "endOffsets": "9143,9269,9395,9518,9618,9712,9823,9975,10093,10250,10335,10440,10540,10642,10765,10898,11008,11144,11286,11417,11621,11755,11879,12009,12143,12244,12342,12460,12591,12690,12792,12905,13043,13189,13303,13412,13488,13586,13686,13800,13887,13984,14092,14172,14260,14358,14471,14566,14677,14767,14882,14984,15097,15229,15309,15416,15513"}}]}]}