package com.falaileh.elmarjo

import android.os.Bundle
import android.view.WindowManager
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat
import androidx.lifecycle.viewmodel.compose.viewModel
import com.falaileh.elmarjo.ui.screens.FlagGridScreen
import com.falaileh.elmarjo.ui.screens.FolderPickerScreen
import com.falaileh.elmarjo.ui.screens.VideoPlayerScreen
import com.falaileh.elmarjo.ui.theme.ElmarjoTheme
import com.falaileh.elmarjo.viewmodel.MainViewModel
import com.falaileh.elmarjo.viewmodel.Screen

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Enable fullscreen mode
        setupFullscreen()

        setContent {
            ElmarjoTheme {
                ElmarjoApp()
            }
        }
    }

    private fun setupFullscreen() {
        // Hide system UI for fullscreen experience
        WindowCompat.setDecorFitsSystemWindows(window, false)
        val controller = WindowInsetsControllerCompat(window, window.decorView)
        controller.hide(WindowInsetsCompat.Type.systemBars())
        controller.systemBarsBehavior = WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE

        // Keep screen on
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
    }
}

@Composable
fun ElmarjoApp() {
    val viewModel: MainViewModel = viewModel()
    val uiState by viewModel.uiState.collectAsState()

    when (uiState.currentScreen) {
        Screen.FolderPicker -> {
            FolderPickerScreen(
                onFolderSelected = viewModel::onFolderSelected
            )
        }

        Screen.FlagGrid -> {
            FlagGridScreen(
                onLanguageSelected = viewModel::onLanguageSelected,
                onSettingsClick = viewModel::navigateToSettings
            )
        }

        Screen.VideoPlayer -> {
            uiState.selectedLanguage?.let { language ->
                VideoPlayerScreen(
                    language = language,
                    onVideoCompleted = viewModel::onVideoCompleted
                )
            }
        }
    }
}