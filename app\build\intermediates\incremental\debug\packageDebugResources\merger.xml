<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\elmarjo\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\elmarjo\app\src\main\res"><file name="cn" path="E:\elmarjo\app\src\main\res\drawable\cn.png" qualifiers="" type="drawable"/><file name="cz" path="E:\elmarjo\app\src\main\res\drawable\cz.png" qualifiers="" type="drawable"/><file name="de" path="E:\elmarjo\app\src\main\res\drawable\de.png" qualifiers="" type="drawable"/><file name="es" path="E:\elmarjo\app\src\main\res\drawable\es.png" qualifiers="" type="drawable"/><file name="fr" path="E:\elmarjo\app\src\main\res\drawable\fr.png" qualifiers="" type="drawable"/><file name="gb" path="E:\elmarjo\app\src\main\res\drawable\gb.png" qualifiers="" type="drawable"/><file name="gr" path="E:\elmarjo\app\src\main\res\drawable\gr.png" qualifiers="" type="drawable"/><file name="hu" path="E:\elmarjo\app\src\main\res\drawable\hu.png" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="E:\elmarjo\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="E:\elmarjo\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="id" path="E:\elmarjo\app\src\main\res\drawable\id.png" qualifiers="" type="drawable"/><file name="in" path="E:\elmarjo\app\src\main\res\drawable\in.png" qualifiers="" type="drawable"/><file name="it" path="E:\elmarjo\app\src\main\res\drawable\it.png" qualifiers="" type="drawable"/><file name="jo" path="E:\elmarjo\app\src\main\res\drawable\jo.png" qualifiers="" type="drawable"/><file name="jp" path="E:\elmarjo\app\src\main\res\drawable\jp.png" qualifiers="" type="drawable"/><file name="kr" path="E:\elmarjo\app\src\main\res\drawable\kr.png" qualifiers="" type="drawable"/><file name="logo" path="E:\elmarjo\app\src\main\res\drawable\logo.png" qualifiers="" type="drawable"/><file name="nl" path="E:\elmarjo\app\src\main\res\drawable\nl.png" qualifiers="" type="drawable"/><file name="pl" path="E:\elmarjo\app\src\main\res\drawable\pl.png" qualifiers="" type="drawable"/><file name="pt" path="E:\elmarjo\app\src\main\res\drawable\pt.png" qualifiers="" type="drawable"/><file name="ro" path="E:\elmarjo\app\src\main\res\drawable\ro.png" qualifiers="" type="drawable"/><file name="ru" path="E:\elmarjo\app\src\main\res\drawable\ru.png" qualifiers="" type="drawable"/><file name="sk" path="E:\elmarjo\app\src\main\res\drawable\sk.png" qualifiers="" type="drawable"/><file name="tr" path="E:\elmarjo\app\src\main\res\drawable\tr.png" qualifiers="" type="drawable"/><file name="ua" path="E:\elmarjo\app\src\main\res\drawable\ua.png" qualifiers="" type="drawable"/><file name="ic_launcher" path="E:\elmarjo\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="E:\elmarjo\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="E:\elmarjo\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="E:\elmarjo\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="E:\elmarjo\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="E:\elmarjo\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="E:\elmarjo\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="E:\elmarjo\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="E:\elmarjo\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="E:\elmarjo\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="E:\elmarjo\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="E:\elmarjo\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="E:\elmarjo\app\src\main\res\values\colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color></file><file path="E:\elmarjo\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">elmarjo</string></file><file path="E:\elmarjo\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.Elmarjo" parent="android:Theme.Material.Light.NoActionBar"/></file><file name="backup_rules" path="E:\elmarjo\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="E:\elmarjo\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\elmarjo\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\elmarjo\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\elmarjo\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\elmarjo\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>