"-Xallow-no-source-files" "-classpath" "E:\\elmarjo\\app\\build\\intermediates\\compile_and_runtime_not_namespaced_r_class_jar\\debug\\processDebugResources\\R.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d8fac5e1444df3336028f8c961a3ce21\\transformed\\navigation-common-2.7.6-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8b8caef35d01a772f523d47556e605e3\\transformed\\navigation-runtime-2.7.6-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fd570d41b85a7d4664177aca346f17aa\\transformed\\navigation-common-ktx-2.7.6-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e1f5984af5fe38479e7e886f8775c698\\transformed\\navigation-runtime-ktx-2.7.6-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d9ab31021c74aee2b9b4700538c1a3b0\\transformed\\navigation-compose-2.7.6-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\efff865d2bee1cee51442380e25712b4\\transformed\\material3-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0ab0858a0021f34a8ab3beb04133d095\\transformed\\foundation-layout-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\48b23f225e392c3d65eb9e50801dd23d\\transformed\\material-ripple-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\222e6f27033a92c5b88f5c694053dde7\\transformed\\foundation-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0f9db53259b7e2d9f19374ce856fbd5e\\transformed\\animation-core-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\199c3173d74f55d0fcd7343d40e2f329\\transformed\\animation-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\15c4b065b05aa507fb7fa89271277fb0\\transformed\\ui-util-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c9de78199cbcb7d5f42f723d02af2007\\transformed\\ui-unit-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af69fba6946770126ab198b352d48360\\transformed\\ui-text-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\04411974576e73b41be513f966cb8d69\\transformed\\ui-geometry-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\61ccbe2b0202f46ce02003833b79e971\\transformed\\ui-tooling-data-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a0f6d2910dc24aea213516c9f308b8b1\\transformed\\ui-tooling-preview-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ecb842407c1a5d1f768b7c183493c610\\transformed\\ui-graphics-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bf2cb479d373b9e56da16d92693431d7\\transformed\\preference-ktx-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8eff47986f27473c16474c283266811e\\transformed\\preference-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\19afc1f3d11c34a5af49fbe9b0200738\\transformed\\fragment-ktx-1.3.6-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2e2a2b562b8d98763785d16dd8826feb\\transformed\\activity-ktx-1.10.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fe9d408d5a142f4f32e38da5717a409d\\transformed\\appcompat-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\474f65dcea61a0238eb7e688e661f166\\transformed\\recyclerview-1.3.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a1e06451eb95ecf9579ccf88a7ff94e4\\transformed\\fragment-1.3.6-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\54a90c073dc9e9a6148e0a4db4e5e97c\\transformed\\appcompat-resources-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cf429bba0ff9903f930c0a81bd352e1e\\transformed\\drawerlayout-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c3e7b5d4b5097c1b5510639d859cb845\\transformed\\slidingpanelayout-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f44d5a34f375238118a042b9ad0b1f7f\\transformed\\viewpager-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5ca480725ae6d6386091d4b308d5104c\\transformed\\customview-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\57ca9e5efd7f250e498bb8834794d5ce\\transformed\\loader-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d9897f8982eeb329aa8768e1096b09fd\\transformed\\vectordrawable-animated-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cb99c14b9c177b24dc12e506119119d7\\transformed\\vectordrawable-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\245c3fd37473819bb16b4080eb4180a3\\transformed\\core-1.16.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\1e00ffc981a3b17a5e874ef68c65c2ed\\transformed\\lifecycle-livedata-2.9.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8b6e0525321e0c69fe9b17929b513299\\transformed\\lifecycle-livedata-core-2.9.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7a92a9c433cb9419566b9b760d25dcfe\\transformed\\lifecycle-runtime-compose-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2ab82bb4a727b5fae91ce676f8a0f3c5\\transformed\\lifecycle-viewmodel-savedstate-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\51f5784c47fdadee5a4d58b333128e98\\transformed\\lifecycle-runtime-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common-jvm\\2.9.2\\ab739bccdb3541983385af169565c7c035d897e7\\lifecycle-common-jvm-2.9.2.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a3340a8599b9e27eb5edd3a6ca9a1b2d\\transformed\\lifecycle-livedata-core-ktx-2.9.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8f22f7f096d871450f10242fbbaae9f9\\transformed\\lifecycle-viewmodel-2.9.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\35e53dcd2e29f165346e85b41e69121e\\transformed\\lifecycle-viewmodel-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\52465a92c2142458a82dc52e37697f22\\transformed\\lifecycle-viewmodel-ktx-2.9.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b5aa0f94473f7e110520e00cacee1b4c\\transformed\\lifecycle-runtime-ktx-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\eb7d23a310ce8b0f5b78b5f809dd0906\\transformed\\lifecycle-viewmodel-compose-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e565f43a5eedeb6997fa3e1b7b7e7870\\transformed\\material-icons-core-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4efe507ff169c971732d052d384e0345\\transformed\\ui-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\40ca267e969148e408e24d87e026958b\\transformed\\ui-tooling-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\553b48c1d8afd8f7e248038b87a263dc\\transformed\\ui-test-manifest-1.7.8-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\1f44ad38e01e7a3346dbf34a4aede384\\transformed\\activity-1.10.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c9df673ae2800f04cacc13fb7452bbda\\transformed\\activity-compose-1.10.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0afc7bba0aa6505f0ce167d8b9519d79\\transformed\\core-ktx-1.16.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9093ba01737746f3e0321deafc4a083e\\transformed\\media3-exoplayer-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\56c64a0871249d69fab42838c41deaed\\transformed\\media3-common-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fb955aedc259ac3d1969754aeef50df8\\transformed\\runtime-saveable-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9b3b1e5c4d0867febd4100ae24beee2c\\transformed\\runtime-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\3cd9eaef3422068127dbec3eaa27e6ae\\transformed\\annotation-experimental-1.4.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\33d33ef6c4a2bf7a9f506ab48d8fb824\\transformed\\core-viewtree-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7a6b4383f3416296f3feab7c7cecd0fa\\transformed\\documentfile-1.0.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f6af8c3d75989cafba3b94397dfeea4d\\transformed\\versionedparcelable-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9ff71fd97d55664c4303fb59f19b9c7e\\transformed\\savedstate-ktx-1.3.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\67e7ce1e45ac146cdc91d7307f641e81\\transformed\\savedstate-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f1ace51bf11e1264dfbf7a8e1c887c37\\transformed\\cursoradapter-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.collection\\collection-ktx\\1.4.4\\2ad14aed781c4a73ed4dbb421966d408a0a06686\\collection-ktx-1.4.4.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.collection\\collection-jvm\\1.4.4\\da13a7e557c430276b8cb490420effebc1398c0d\\collection-jvm-1.4.4.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\56a7278c4f6391ce58d63dd5198dcac1\\transformed\\core-runtime-2.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.arch.core\\core-common\\2.2.0\\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\\core-common-2.2.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4455314b4eabaee5227f55371d9e8eb2\\transformed\\interpolator-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.annotation\\annotation-jvm\\1.9.1\\b17951747e38bf3986a24431b9ba0d039958aa5f\\annotation-jvm-1.9.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlinx\\kotlinx-coroutines-core-jvm\\1.8.1\\bb0e192bd7c2b6b8217440d36e9758e377e450\\kotlinx-coroutines-core-jvm-1.8.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlinx\\kotlinx-coroutines-android\\1.8.1\\73e2acdd18df99dd4849d99f188dff529fc0afe0\\kotlinx-coroutines-android-1.8.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlinx\\kotlinx-serialization-core-jvm\\1.7.3\\1f226780b845ff9206474c05159245d861556249\\kotlinx-serialization-core-jvm-1.7.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-stdlib\\2.0.21\\618b539767b4899b4660a83006e052b63f1db551\\kotlin-stdlib-2.0.21.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\512c2989cf84446b5f49bb8d15faf6dd\\transformed\\media3-ui-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains\\annotations\\23.0.0\\8cc20c07506ec18e0834947b84a864bfc094484e\\annotations-23.0.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.guava\\guava\\31.1-android\\9222c47cc3ae890f07f7c961bbb3cb69050fe4aa\\guava-31.1-android.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.guava\\listenablefuture\\9999.0-empty-to-avoid-conflict-with-guava\\b421526c5f297295adef1c886e5246c39d4ac629\\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jspecify\\jspecify\\1.0.0\\7425a601c1c7ec76645a78d22b8c6a627edee507\\jspecify-1.0.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.guava\\failureaccess\\1.0.1\\1dcf1de382a0bf95a3d8b0849546c88bac1292c9\\failureaccess-1.0.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8168a35a2f608a254f9ac093650b9bb7\\transformed\\media3-container-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7ad0eafaff94182b85f3b37966874afa\\transformed\\media3-database-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5d0424f7cec2806f103a30644dbd56cf\\transformed\\media3-datasource-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\302c75420528ad4aba137a467290f56a\\transformed\\media3-decoder-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b46be705d50d88c09d9c43ee37aa2a68\\transformed\\media3-extractor-1.2.1-api.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\platforms\\android-36\\android.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\build-tools\\35.0.0\\core-lambda-stubs.jar" "-d" "E:\\elmarjo\\app\\build\\tmp\\kotlin-classes\\debug" "-jvm-target" "11" "-module-name" "app_debug" "-no-jdk" "-no-reflect" "-no-stdlib" "-Xplugin=C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-compose-compiler-plugin-embeddable\\2.0.21\\e14f003d962fb25693b461de59490c91072a7979\\kotlin-compose-compiler-plugin-embeddable-2.0.21.jar" "-P" "plugin:androidx.compose.compiler.plugins.kotlin:sourceInformation=true,plugin:androidx.compose.compiler.plugins.kotlin:generateFunctionKeyMetaClasses=false,plugin:androidx.compose.compiler.plugins.kotlin:traceMarkersEnabled=true" "-Xuse-inline-scopes-numbers" "-Xallow-unstable-dependencies" "E:\\elmarjo\\app\\src\\main\\java\\com\\falaileh\\elmarjo\\data\\Language.kt" "E:\\elmarjo\\app\\src\\main\\java\\com\\falaileh\\elmarjo\\MainActivity.kt" "E:\\elmarjo\\app\\src\\main\\java\\com\\falaileh\\elmarjo\\repository\\FolderManager.kt" "E:\\elmarjo\\app\\src\\main\\java\\com\\falaileh\\elmarjo\\ui\\screens\\FlagGridScreen.kt" "E:\\elmarjo\\app\\src\\main\\java\\com\\falaileh\\elmarjo\\ui\\screens\\FolderPickerScreen.kt" "E:\\elmarjo\\app\\src\\main\\java\\com\\falaileh\\elmarjo\\ui\\screens\\VideoPlayerScreen.kt" "E:\\elmarjo\\app\\src\\main\\java\\com\\falaileh\\elmarjo\\ui\\theme\\Color.kt" "E:\\elmarjo\\app\\src\\main\\java\\com\\falaileh\\elmarjo\\ui\\theme\\Theme.kt" "E:\\elmarjo\\app\\src\\main\\java\\com\\falaileh\\elmarjo\\ui\\theme\\Type.kt" "E:\\elmarjo\\app\\src\\main\\java\\com\\falaileh\\elmarjo\\utils\\TVUtils.kt" "E:\\elmarjo\\app\\src\\main\\java\\com\\falaileh\\elmarjo\\viewmodel\\MainViewModel.kt"