{"logs": [{"outputFile": "com.falaileh.elmarjo.app-mergeDebugResources-65:/values-si/values-si.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\3109e90bbcb133a6123d6a171e482e61\\transformed\\foundation-release\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,92", "endOffsets": "140,233"}, "to": {"startLines": "186,187", "startColumns": "4,4", "startOffsets": "16365,16455", "endColumns": "89,92", "endOffsets": "16450,16543"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5510098f7f655104979ce51ebf04d4e9\\transformed\\media3-exoplayer-1.2.1\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,191,254,320,396,465,554,640", "endColumns": "75,59,62,65,75,68,88,85,69", "endOffsets": "126,186,249,315,391,460,549,635,705"}, "to": {"startLines": "83,84,85,86,87,88,89,90,91", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6715,6791,6851,6914,6980,7056,7125,7214,7300", "endColumns": "75,59,62,65,75,68,88,85,69", "endOffsets": "6786,6846,6909,6975,7051,7120,7209,7295,7365"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d0e5df3da8374bed71d9993cb807cb2d\\transformed\\core-1.16.0\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,365,470,569,673,787", "endColumns": "101,102,104,104,98,103,113,100", "endOffsets": "152,255,360,465,564,668,782,883"}, "to": {"startLines": "46,47,48,49,50,51,52,181", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3432,3534,3637,3742,3847,3946,4050,15829", "endColumns": "101,102,104,104,98,103,113,100", "endOffsets": "3529,3632,3737,3842,3941,4045,4159,15925"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\32ffae0c59a3b7f539124213f72623ec\\transformed\\media3-ui-1.2.1\\res\\values-si\\values-si.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,488,666,750,833,914,1007,1099,1162,1224,1313,1404,1475,1545,1606,1672,1811,1953,2090,2161,2240,2310,2375,2465,2554,2621,2689,2742,2800,2847,2908,2968,3035,3096,3161,3220,3285,3354,3417,3484,3538,3595,3666,3737", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,83,82,80,92,91,62,61,88,90,70,69,60,65,138,141,136,70,78,69,64,89,88,66,67,52,57,46,60,59,66,60,64,58,64,68,62,66,53,56,70,70,51", "endOffsets": "282,483,661,745,828,909,1002,1094,1157,1219,1308,1399,1470,1540,1601,1667,1806,1948,2085,2156,2235,2305,2370,2460,2549,2616,2684,2737,2795,2842,2903,2963,3030,3091,3156,3215,3280,3349,3412,3479,3533,3590,3661,3732,3784"}, "to": {"startLines": "2,11,15,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,337,538,4692,4776,4859,4940,5033,5125,5188,5250,5339,5430,5501,5571,5632,5698,5837,5979,6116,6187,6266,6336,6401,6491,6580,6647,7370,7423,7481,7528,7589,7649,7716,7777,7842,7901,7966,8035,8098,8165,8219,8276,8347,8418", "endLines": "10,14,18,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109", "endColumns": "17,12,12,83,82,80,92,91,62,61,88,90,70,69,60,65,138,141,136,70,78,69,64,89,88,66,67,52,57,46,60,59,66,60,64,58,64,68,62,66,53,56,70,70,51", "endOffsets": "332,533,711,4771,4854,4935,5028,5120,5183,5245,5334,5425,5496,5566,5627,5693,5832,5974,6111,6182,6261,6331,6396,6486,6575,6642,6710,7418,7476,7523,7584,7644,7711,7772,7837,7896,7961,8030,8093,8160,8214,8271,8342,8413,8465"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5ae455f1a13db310449e207b49d3bacc\\transformed\\appcompat-1.1.0\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,328,435,518,623,739,829,916,1007,1099,1193,1287,1388,1481,1576,1670,1761,1852,1935,2044,2148,2246,2356,2456,2563,2722,2821", "endColumns": "115,106,106,82,104,115,89,86,90,91,93,93,100,92,94,93,90,90,82,108,103,97,109,99,106,158,98,80", "endOffsets": "216,323,430,513,618,734,824,911,1002,1094,1188,1282,1383,1476,1571,1665,1756,1847,1930,2039,2143,2241,2351,2451,2558,2717,2816,2897"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,176", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "716,832,939,1046,1129,1234,1350,1440,1527,1618,1710,1804,1898,1999,2092,2187,2281,2372,2463,2546,2655,2759,2857,2967,3067,3174,3333,15435", "endColumns": "115,106,106,82,104,115,89,86,90,91,93,93,100,92,94,93,90,90,82,108,103,97,109,99,106,158,98,80", "endOffsets": "827,934,1041,1124,1229,1345,1435,1522,1613,1705,1799,1893,1994,2087,2182,2276,2367,2458,2541,2650,2754,2852,2962,3062,3169,3328,3427,15511"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e754f461ad8f7a511fa7fc6c4385c693\\transformed\\material3-release\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,288,399,516,614,711,825,954,1074,1213,1297,1403,1494,1591,1705,1833,1944,2072,2198,2330,2503,2627,2744,2864,2985,3077,3172,3291,3412,3513,3616,3720,3851,3987,4094,4191,4267,4363,4461,4566,4652,4741,4835,4918,5001,5100,5200,5292,5393,5481,5592,5694,5806,5927,6009,6117", "endColumns": "115,116,110,116,97,96,113,128,119,138,83,105,90,96,113,127,110,127,125,131,172,123,116,119,120,91,94,118,120,100,102,103,130,135,106,96,75,95,97,104,85,88,93,82,82,98,99,91,100,87,110,101,111,120,81,107,98", "endOffsets": "166,283,394,511,609,706,820,949,1069,1208,1292,1398,1489,1586,1700,1828,1939,2067,2193,2325,2498,2622,2739,2859,2980,3072,3167,3286,3407,3508,3611,3715,3846,3982,4089,4186,4262,4358,4456,4561,4647,4736,4830,4913,4996,5095,5195,5287,5388,5476,5587,5689,5801,5922,6004,6112,6211"}, "to": {"startLines": "113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8729,8845,8962,9073,9190,9288,9385,9499,9628,9748,9887,9971,10077,10168,10265,10379,10507,10618,10746,10872,11004,11177,11301,11418,11538,11659,11751,11846,11965,12086,12187,12290,12394,12525,12661,12768,12865,12941,13037,13135,13240,13326,13415,13509,13592,13675,13774,13874,13966,14067,14155,14266,14368,14480,14601,14683,14791", "endColumns": "115,116,110,116,97,96,113,128,119,138,83,105,90,96,113,127,110,127,125,131,172,123,116,119,120,91,94,118,120,100,102,103,130,135,106,96,75,95,97,104,85,88,93,82,82,98,99,91,100,87,110,101,111,120,81,107,98", "endOffsets": "8840,8957,9068,9185,9283,9380,9494,9623,9743,9882,9966,10072,10163,10260,10374,10502,10613,10741,10867,10999,11172,11296,11413,11533,11654,11746,11841,11960,12081,12182,12285,12389,12520,12656,12763,12860,12936,13032,13130,13235,13321,13410,13504,13587,13670,13769,13869,13961,14062,14150,14261,14363,14475,14596,14678,14786,14885"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\eb5978be55e4593326b90344069d1b2d\\transformed\\ui-release\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,194,277,376,475,557,642,733,819,899,978,1060,1133,1208,1292,1373,1454,1521", "endColumns": "88,82,98,98,81,84,90,85,79,78,81,72,74,83,80,80,66,117", "endOffsets": "189,272,371,470,552,637,728,814,894,973,1055,1128,1203,1287,1368,1449,1516,1634"}, "to": {"startLines": "53,54,56,57,58,111,112,170,171,174,175,177,178,179,180,183,184,185", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4164,4253,4412,4511,4610,8553,8638,14890,14976,15274,15353,15516,15589,15664,15748,16099,16180,16247", "endColumns": "88,82,98,98,81,84,90,85,79,78,81,72,74,83,80,80,66,117", "endOffsets": "4248,4331,4506,4605,4687,8633,8724,14971,15051,15348,15430,15584,15659,15743,15824,16175,16242,16360"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d5341083af57bd2b42f46054ffafb559\\transformed\\preference-1.2.1\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,181,264,339,482,651,743", "endColumns": "75,82,74,142,168,91,86", "endOffsets": "176,259,334,477,646,738,825"}, "to": {"startLines": "55,110,172,173,182,188,189", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4336,8470,15056,15131,15930,16548,16640", "endColumns": "75,82,74,142,168,91,86", "endOffsets": "4407,8548,15126,15269,16094,16635,16722"}}]}]}