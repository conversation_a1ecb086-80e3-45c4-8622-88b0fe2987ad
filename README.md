# Elmarjo - Android TV Video Player App

A regular Android app designed to run on Android TV devices with manual portrait rotation. The app displays a grid of country flags and plays corresponding video files.

## Features

- **Manual Portrait Rotation**: The entire UI is rotated 90 degrees to appear in portrait mode on a landscape TV
- **Folder Selection**: Uses Storage Access Framework (SAF) to select folders containing video files
- **Flag Grid**: Displays 22 country flags in a grid layout with D-Pad navigation support
- **Video Playback**: Plays videos in fullscreen using ExoPlayer
- **TV Optimized**: Designed for Android TV remote control navigation

## Setup Instructions

### 1. Add Flag Images
You need to add actual flag images to the `app/src/main/res/drawable/` folder. The current implementation uses placeholder icons.

Create flag images with these naming conventions:
- `flag_en.png` (English/United States)
- `flag_fr.png` (French/France)
- `flag_ar.png` (Arabic/Saudi Arabia)
- `flag_es.png` (Spanish/Spain)
- `flag_de.png` (German/Germany)
- `flag_it.png` (Italian/Italy)
- `flag_pt.png` (Portuguese/Portugal)
- `flag_ru.png` (Russian/Russia)
- `flag_zh.png` (Chinese/China)
- `flag_ja.png` (Japanese/Japan)
- `flag_ko.png` (Korean/South Korea)
- `flag_hi.png` (Hindi/India)
- `flag_tr.png` (Turkish/Turkey)
- `flag_nl.png` (Dutch/Netherlands)
- `flag_sv.png` (Swedish/Sweden)
- `flag_no.png` (Norwegian/Norway)
- `flag_da.png` (Danish/Denmark)
- `flag_fi.png` (Finnish/Finland)
- `flag_pl.png` (Polish/Poland)
- `flag_cs.png` (Czech/Czech Republic)
- `flag_hu.png` (Hungarian/Hungary)
- `flag_ro.png` (Romanian/Romania)

Then update the `Language.kt` file to reference these resources instead of the placeholder icons.

### 2. Prepare Video Files
Create a folder on your Android TV device (USB drive or internal storage) containing 22 video files:
- `en.mp4`, `fr.mp4`, `ar.mp4`, `es.mp4`, `de.mp4`, `it.mp4`, `pt.mp4`, `ru.mp4`, `zh.mp4`, `ja.mp4`, `ko.mp4`, `hi.mp4`, `tr.mp4`, `nl.mp4`, `sv.mp4`, `no.mp4`, `da.mp4`, `fi.mp4`, `pl.mp4`, `cs.mp4`, `hu.mp4`, `ro.mp4`

### 3. Build and Install
1. Build the APK: `./gradlew assembleDebug`
2. Install on Android TV: `adb install app/build/outputs/apk/debug/app-debug.apk`

## Testing

### Android TV Emulator
1. Create an Android TV emulator in Android Studio
2. Install and run the app
3. Use the emulator's D-Pad controls to navigate

### Physical Android TV Device
1. Enable Developer Options and USB Debugging
2. Connect via ADB
3. Install the APK
4. Test with a physical remote control

### Testing Checklist
- [ ] App launches in fullscreen mode
- [ ] UI appears rotated 90 degrees (portrait on landscape TV)
- [ ] Folder picker opens and allows folder selection
- [ ] Flag grid displays with proper D-Pad navigation
- [ ] Focus indication works with remote control
- [ ] Video playback works in fullscreen
- [ ] Videos return to flag grid after completion
- [ ] App remembers selected folder on restart

## Architecture

- **MainActivity**: Main entry point with fullscreen setup
- **MainViewModel**: Manages app state and navigation
- **FolderManager**: Handles Storage Access Framework operations
- **Screens**: Compose screens for folder picker, flag grid, and video player
- **Language**: Data model for countries and video file mapping

## Key Technologies

- Jetpack Compose for UI
- ExoPlayer for video playback
- Storage Access Framework for file access
- Android TV focus handling
- Manual UI rotation using `graphicsLayer { rotationZ = 90f }`

## Notes

- The app is built as a regular Android app, not using Leanback libraries
- Portrait orientation is achieved through manual UI rotation, not system rotation
- The app supports both USB drives and internal storage through SAF
- All navigation is optimized for D-Pad/remote control input
