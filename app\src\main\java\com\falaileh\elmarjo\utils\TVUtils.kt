package com.falaileh.elmarjo.utils

import androidx.compose.foundation.border
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp

/**
 * Utility functions for Android TV optimization
 */
object TVUtils {
    
    /**
     * Modifier for TV-style focus indication
     */
    @Composable
    fun Modifier.tvFocusable(): Modifier {
        var isFocused by remember { mutableStateOf(false) }
        
        return this
            .onFocusChanged { isFocused = it.isFocused }
            .border(
                width = if (isFocused) 3.dp else 0.dp,
                color = if (isFocused) Color.White else Color.Transparent,
                shape = RoundedCornerShape(8.dp)
            )
    }
    
    /**
     * Get focus border color based on focus state
     */
    fun getFocusBorderColor(isFocused: Boolean): Color {
        return if (isFocused) Color.White else Color.Transparent
    }
    
    /**
     * Get focus border width based on focus state
     */
    fun getFocusBorderWidth(isFocused: Boolean): androidx.compose.ui.unit.Dp {
        return if (isFocused) 3.dp else 0.dp
    }
    
    /**
     * Get background color for focused/unfocused state
     */
    fun getBackgroundColor(isFocused: Boolean): Color {
        return if (isFocused) Color.Gray.copy(alpha = 0.3f) else Color.Gray.copy(alpha = 0.1f)
    }
}
