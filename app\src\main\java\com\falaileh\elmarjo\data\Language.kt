package com.falaileh.elmarjo.data

import androidx.annotation.DrawableRes
import com.falaileh.elmarjo.R

/**
 * Data class representing a language/country with its flag and video file
 */
data class Language(
    val code: String,           // e.g., "en", "fr", "ar"
    val name: String,           // e.g., "English", "French", "Arabic"
    val countryName: String,    // e.g., "United States", "France", "Saudi Arabia"
    @DrawableRes val flagRes: Int,  // Resource ID for flag drawable
    val videoFileName: String   // e.g., "en.mp4", "fr.mp4", "ar.mp4"
)

/**
 * Constants and predefined language data
 */
object LanguageConstants {
    
    // List of all supported languages with actual flag resources
    val LANGUAGES = listOf(
        Language("en", "English", "United Kingdom", R.drawable.gb, "en.mp4"),
        Language("fr", "French", "France", R.drawable.fr, "fr.mp4"),
        Language("ar", "Arabic", "Jordan", <PERSON>.drawable.jo, "ar.mp4"),
        Language("es", "Spanish", "Spain", R.drawable.es, "es.mp4"),
        Language("de", "German", "Germany", R.drawable.de, "de.mp4"),
        Language("it", "Italian", "Italy", R.drawable.it, "it.mp4"),
        Language("pt", "Portuguese", "Portugal", R.drawable.pt, "pt.mp4"),
        Language("ru", "Russian", "Russia", R.drawable.ru, "ru.mp4"),
        Language("zh", "Chinese", "China", R.drawable.cn, "zh.mp4"),
        Language("ja", "Japanese", "Japan", R.drawable.jp, "ja.mp4"),
        Language("ko", "Korean", "South Korea", R.drawable.kr, "ko.mp4"),
        Language("hi", "Hindi", "India", R.drawable.`in`, "hi.mp4"),
        Language("tr", "Turkish", "Turkey", R.drawable.tr, "tr.mp4"),
        Language("nl", "Dutch", "Netherlands", R.drawable.nl, "nl.mp4"),
        Language("gr", "Greek", "Greece", R.drawable.gr, "gr.mp4"),
        Language("ua", "Ukrainian", "Ukraine", R.drawable.ua, "ua.mp4"),
        Language("id", "Indonesian", "Indonesia", R.drawable.id, "id.mp4"),
        Language("pl", "Polish", "Poland", R.drawable.pl, "pl.mp4"),
        Language("cs", "Czech", "Czech Republic", R.drawable.cz, "cs.mp4"),
        Language("hu", "Hungarian", "Hungary", R.drawable.hu, "hu.mp4"),
        Language("ro", "Romanian", "Romania", R.drawable.ro, "ro.mp4"),
        Language("sk", "Slovak", "Slovakia", R.drawable.sk, "sk.mp4")
    )
    
    /**
     * Get language by code
     */
    fun getLanguageByCode(code: String): Language? {
        return LANGUAGES.find { it.code == code }
    }
    
    /**
     * Get language by video file name
     */
    fun getLanguageByVideoFileName(fileName: String): Language? {
        return LANGUAGES.find { it.videoFileName == fileName }
    }
}
