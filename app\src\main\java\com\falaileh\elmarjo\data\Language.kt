package com.falaileh.elmarjo.data

import androidx.annotation.DrawableRes

/**
 * Data class representing a language/country with its flag and video file
 */
data class Language(
    val code: String,           // e.g., "en", "fr", "ar"
    val name: String,           // e.g., "English", "French", "Arabic"
    val countryName: String,    // e.g., "United States", "France", "Saudi Arabia"
    @DrawableRes val flagRes: Int,  // Resource ID for flag drawable
    val videoFileName: String   // e.g., "en.mp4", "fr.mp4", "ar.mp4"
)

/**
 * Constants and predefined language data
 */
object LanguageConstants {
    
    // List of all 22 supported languages
    // Note: You need to add actual flag images to res/drawable/ folder
    // For now, using system icons as placeholders
    val LANGUAGES = listOf(
        Language("en", "English", "United States", android.R.drawable.star_big_on, "en.mp4"),
        Language("fr", "French", "France", android.R.drawable.star_big_on, "fr.mp4"),
        Language("ar", "Arabic", "Saudi Arabia", android.R.drawable.star_big_on, "ar.mp4"),
        Language("es", "Spanish", "Spain", android.R.drawable.star_big_on, "es.mp4"),
        Language("de", "German", "Germany", android.R.drawable.star_big_on, "de.mp4"),
        Language("it", "Italian", "Italy", android.R.drawable.star_big_on, "it.mp4"),
        Language("pt", "Portuguese", "Portugal", android.R.drawable.star_big_on, "pt.mp4"),
        Language("ru", "Russian", "Russia", android.R.drawable.star_big_on, "ru.mp4"),
        Language("zh", "Chinese", "China", android.R.drawable.star_big_on, "zh.mp4"),
        Language("ja", "Japanese", "Japan", android.R.drawable.star_big_on, "ja.mp4"),
        Language("ko", "Korean", "South Korea", android.R.drawable.star_big_on, "ko.mp4"),
        Language("hi", "Hindi", "India", android.R.drawable.star_big_on, "hi.mp4"),
        Language("tr", "Turkish", "Turkey", android.R.drawable.star_big_on, "tr.mp4"),
        Language("nl", "Dutch", "Netherlands", android.R.drawable.star_big_on, "nl.mp4"),
        Language("sv", "Swedish", "Sweden", android.R.drawable.star_big_on, "sv.mp4"),
        Language("no", "Norwegian", "Norway", android.R.drawable.star_big_on, "no.mp4"),
        Language("da", "Danish", "Denmark", android.R.drawable.star_big_on, "da.mp4"),
        Language("fi", "Finnish", "Finland", android.R.drawable.star_big_on, "fi.mp4"),
        Language("pl", "Polish", "Poland", android.R.drawable.star_big_on, "pl.mp4"),
        Language("cs", "Czech", "Czech Republic", android.R.drawable.star_big_on, "cs.mp4"),
        Language("hu", "Hungarian", "Hungary", android.R.drawable.star_big_on, "hu.mp4"),
        Language("ro", "Romanian", "Romania", android.R.drawable.star_big_on, "ro.mp4")
    )
    
    /**
     * Get language by code
     */
    fun getLanguageByCode(code: String): Language? {
        return LANGUAGES.find { it.code == code }
    }
    
    /**
     * Get language by video file name
     */
    fun getLanguageByVideoFileName(fileName: String): Language? {
        return LANGUAGES.find { it.videoFileName == fileName }
    }
}
