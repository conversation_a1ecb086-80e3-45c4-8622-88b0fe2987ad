package com.falaileh.elmarjo.ui.screens

import android.net.Uri
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.viewinterop.AndroidView
import androidx.media3.common.MediaItem
import androidx.media3.common.Player
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.ui.PlayerView
import com.falaileh.elmarjo.data.Language
import com.falaileh.elmarjo.repository.FolderManager
import kotlinx.coroutines.launch

@Composable
fun VideoPlayerScreen(
    language: Language,
    onVideoCompleted: () -> Unit
) {
    val context = LocalContext.current
    val folderManager = remember { FolderManager(context) }
    val scope = rememberCoroutineScope()
    
    var videoUri by remember { mutableStateOf<Uri?>(null) }
    var isLoading by remember { mutableStateOf(true) }
    var errorMessage by remember { mutableStateOf<String?>(null) }
    
    // Create ExoPlayer
    val exoPlayer = remember {
        ExoPlayer.Builder(context).build().apply {
            // Add listener for playback completion
            addListener(object : Player.Listener {
                override fun onPlaybackStateChanged(playbackState: Int) {
                    if (playbackState == Player.STATE_ENDED) {
                        onVideoCompleted()
                    }
                }
            })
        }
    }
    
    // Load video file
    LaunchedEffect(language) {
        scope.launch {
            try {
                val videoFile = folderManager.getVideoFile(language.videoFileName)
                if (videoFile != null && videoFile.exists()) {
                    videoUri = videoFile.uri
                    isLoading = false
                } else {
                    errorMessage = "Video file not found: ${language.videoFileName}"
                    isLoading = false
                }
            } catch (e: Exception) {
                errorMessage = "Error loading video: ${e.message}"
                isLoading = false
            }
        }
    }
    
    // Setup video playback when URI is available
    LaunchedEffect(videoUri) {
        videoUri?.let { uri ->
            val mediaItem = MediaItem.fromUri(uri)
            exoPlayer.setMediaItem(mediaItem)
            exoPlayer.prepare()
            exoPlayer.playWhenReady = true
        }
    }
    
    // Cleanup ExoPlayer
    DisposableEffect(Unit) {
        onDispose {
            exoPlayer.release()
        }
    }
    
    // Apply 90-degree rotation to the entire screen
    Box(
        modifier = Modifier
            .fillMaxSize()
            .graphicsLayer { rotationZ = 90f }
            .background(Color.Black)
    ) {
        when {
            isLoading -> {
                // Loading state
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        CircularProgressIndicator(
                            color = Color.White,
                            modifier = Modifier.size(48.dp)
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Text(
                            text = "Loading ${language.name} video...",
                            color = Color.White
                        )
                    }
                }
            }
            
            errorMessage != null -> {
                // Error state
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = "Error",
                            color = Color.Red,
                            style = MaterialTheme.typography.headlineMedium
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = errorMessage!!,
                            color = Color.White,
                            style = MaterialTheme.typography.bodyMedium
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Button(
                            onClick = onVideoCompleted
                        ) {
                            Text("Go Back")
                        }
                    }
                }
            }
            
            videoUri != null -> {
                // Video player
                AndroidView(
                    factory = { context ->
                        PlayerView(context).apply {
                            player = exoPlayer
                            useController = false // Hide controls for TV experience
                            setShowBuffering(PlayerView.SHOW_BUFFERING_WHEN_PLAYING)
                        }
                    },
                    modifier = Modifier.fillMaxSize()
                )
            }
        }
        
        // Back button overlay (optional, for debugging)
        if (!isLoading && errorMessage == null) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp),
                contentAlignment = Alignment.TopStart
            ) {
                // You can add a back button here if needed for testing
                // Button(onClick = onVideoCompleted) { Text("Back") }
            }
        }
    }
}
