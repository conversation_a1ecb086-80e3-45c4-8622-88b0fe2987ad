{"logs": [{"outputFile": "com.falaileh.elmarjo.app-mergeDebugResources-65:/values-km/values-km.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e754f461ad8f7a511fa7fc6c4385c693\\transformed\\material3-release\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,292,398,514,616,722,845,989,1117,1269,1360,1460,1560,1670,1794,1919,2024,2150,2276,2404,2566,2688,2802,2915,3038,3139,3239,3365,3504,3608,3713,3825,3950,4078,4195,4303,4379,4476,4572,4680,4768,4856,4957,5037,5121,5221,5323,5419,5528,5615,5720,5818,5929,6046,6126,6233", "endColumns": "117,118,105,115,101,105,122,143,127,151,90,99,99,109,123,124,104,125,125,127,161,121,113,112,122,100,99,125,138,103,104,111,124,127,116,107,75,96,95,107,87,87,100,79,83,99,101,95,108,86,104,97,110,116,79,106,99", "endOffsets": "168,287,393,509,611,717,840,984,1112,1264,1355,1455,1555,1665,1789,1914,2019,2145,2271,2399,2561,2683,2797,2910,3033,3134,3234,3360,3499,3603,3708,3820,3945,4073,4190,4298,4374,4471,4567,4675,4763,4851,4952,5032,5116,5216,5318,5414,5523,5610,5715,5813,5924,6041,6121,6228,6328"}, "to": {"startLines": "113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8656,8774,8893,8999,9115,9217,9323,9446,9590,9718,9870,9961,10061,10161,10271,10395,10520,10625,10751,10877,11005,11167,11289,11403,11516,11639,11740,11840,11966,12105,12209,12314,12426,12551,12679,12796,12904,12980,13077,13173,13281,13369,13457,13558,13638,13722,13822,13924,14020,14129,14216,14321,14419,14530,14647,14727,14834", "endColumns": "117,118,105,115,101,105,122,143,127,151,90,99,99,109,123,124,104,125,125,127,161,121,113,112,122,100,99,125,138,103,104,111,124,127,116,107,75,96,95,107,87,87,100,79,83,99,101,95,108,86,104,97,110,116,79,106,99", "endOffsets": "8769,8888,8994,9110,9212,9318,9441,9585,9713,9865,9956,10056,10156,10266,10390,10515,10620,10746,10872,11000,11162,11284,11398,11511,11634,11735,11835,11961,12100,12204,12309,12421,12546,12674,12791,12899,12975,13072,13168,13276,13364,13452,13553,13633,13717,13817,13919,14015,14124,14211,14316,14414,14525,14642,14722,14829,14929"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\eb5978be55e4593326b90344069d1b2d\\transformed\\ui-release\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,190,270,374,472,560,644,727,812,899,979,1064,1140,1214,1286,1357,1441,1507", "endColumns": "84,79,103,97,87,83,82,84,86,79,84,75,73,71,70,83,65,117", "endOffsets": "185,265,369,467,555,639,722,807,894,974,1059,1135,1209,1281,1352,1436,1502,1620"}, "to": {"startLines": "53,54,56,57,58,111,112,170,171,174,175,177,178,179,180,183,184,185", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4113,4198,4347,4451,4549,8489,8573,14934,15019,15317,15397,15565,15641,15715,15787,16128,16212,16278", "endColumns": "84,79,103,97,87,83,82,84,86,79,84,75,73,71,70,83,65,117", "endOffsets": "4193,4273,4446,4544,4632,8568,8651,15014,15101,15392,15477,15636,15710,15782,15853,16207,16273,16391"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5ae455f1a13db310449e207b49d3bacc\\transformed\\appcompat-1.1.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,306,416,503,606,727,805,882,973,1065,1157,1251,1352,1445,1540,1634,1725,1816,1898,2002,2106,2206,2315,2424,2533,2695,2793", "endColumns": "101,98,109,86,102,120,77,76,90,91,91,93,100,92,94,93,90,90,81,103,103,99,108,108,108,161,97,82", "endOffsets": "202,301,411,498,601,722,800,877,968,1060,1152,1246,1347,1440,1535,1629,1720,1811,1893,1997,2101,2201,2310,2419,2528,2690,2788,2871"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,176", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "704,806,905,1015,1102,1205,1326,1404,1481,1572,1664,1756,1850,1951,2044,2139,2233,2324,2415,2497,2601,2705,2805,2914,3023,3132,3294,15482", "endColumns": "101,98,109,86,102,120,77,76,90,91,91,93,100,92,94,93,90,90,81,103,103,99,108,108,108,161,97,82", "endOffsets": "801,900,1010,1097,1200,1321,1399,1476,1567,1659,1751,1845,1946,2039,2134,2228,2319,2410,2492,2596,2700,2800,2909,3018,3127,3289,3387,15560"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5510098f7f655104979ce51ebf04d4e9\\transformed\\media3-exoplayer-1.2.1\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,185,251,320,393,464,562,657", "endColumns": "70,58,65,68,72,70,97,94,68", "endOffsets": "121,180,246,315,388,459,557,652,721"}, "to": {"startLines": "83,84,85,86,87,88,89,90,91", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6594,6665,6724,6790,6859,6932,7003,7101,7196", "endColumns": "70,58,65,68,72,70,97,94,68", "endOffsets": "6660,6719,6785,6854,6927,6998,7096,7191,7260"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d0e5df3da8374bed71d9993cb807cb2d\\transformed\\core-1.16.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,253,351,451,552,664,776", "endColumns": "94,102,97,99,100,111,111,100", "endOffsets": "145,248,346,446,547,659,771,872"}, "to": {"startLines": "46,47,48,49,50,51,52,181", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3392,3487,3590,3688,3788,3889,4001,15858", "endColumns": "94,102,97,99,100,111,111,100", "endOffsets": "3482,3585,3683,3783,3884,3996,4108,15954"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\32ffae0c59a3b7f539124213f72623ec\\transformed\\media3-ui-1.2.1\\res\\values-km\\values-km.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,470,654,731,807,886,970,1058,1139,1205,1292,1381,1446,1508,1570,1636,1763,1886,2009,2081,2166,2237,2320,2402,2483,2545,2611,2664,2722,2772,2833,2892,2960,3030,3099,3166,3225,3291,3356,3423,3478,3535,3612,3689", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,76,75,78,83,87,80,65,86,88,64,61,61,65,126,122,122,71,84,70,82,81,80,61,65,52,57,49,60,58,67,69,68,66,58,65,64,66,54,56,76,76,55", "endOffsets": "280,465,649,726,802,881,965,1053,1134,1200,1287,1376,1441,1503,1565,1631,1758,1881,2004,2076,2161,2232,2315,2397,2478,2540,2606,2659,2717,2767,2828,2887,2955,3025,3094,3161,3220,3286,3351,3418,3473,3530,3607,3684,3740"}, "to": {"startLines": "2,11,15,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,520,4637,4714,4790,4869,4953,5041,5122,5188,5275,5364,5429,5491,5553,5619,5746,5869,5992,6064,6149,6220,6303,6385,6466,6528,7265,7318,7376,7426,7487,7546,7614,7684,7753,7820,7879,7945,8010,8077,8132,8189,8266,8343", "endLines": "10,14,18,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109", "endColumns": "17,12,12,76,75,78,83,87,80,65,86,88,64,61,61,65,126,122,122,71,84,70,82,81,80,61,65,52,57,49,60,58,67,69,68,66,58,65,64,66,54,56,76,76,55", "endOffsets": "330,515,699,4709,4785,4864,4948,5036,5117,5183,5270,5359,5424,5486,5548,5614,5741,5864,5987,6059,6144,6215,6298,6380,6461,6523,6589,7313,7371,7421,7482,7541,7609,7679,7748,7815,7874,7940,8005,8072,8127,8184,8261,8338,8394"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d5341083af57bd2b42f46054ffafb559\\transformed\\preference-1.2.1\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,174,264,342,475,644,724", "endColumns": "68,89,77,132,168,79,76", "endOffsets": "169,259,337,470,639,719,796"}, "to": {"startLines": "55,110,172,173,182,188,189", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4278,8399,15106,15184,15959,16579,16659", "endColumns": "68,89,77,132,168,79,76", "endOffsets": "4342,8484,15179,15312,16123,16654,16731"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\3109e90bbcb133a6123d6a171e482e61\\transformed\\foundation-release\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,146", "endColumns": "90,91", "endOffsets": "141,233"}, "to": {"startLines": "186,187", "startColumns": "4,4", "startOffsets": "16396,16487", "endColumns": "90,91", "endOffsets": "16482,16574"}}]}]}