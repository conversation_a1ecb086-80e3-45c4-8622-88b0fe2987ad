package com.falaileh.elmarjo.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.falaileh.elmarjo.data.Language
import com.falaileh.elmarjo.repository.FolderManager
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * ViewModel for managing app state and navigation
 */
class MainViewModel(application: Application) : AndroidViewModel(application) {
    
    private val folderManager = FolderManager(application)
    
    // UI State
    private val _uiState = MutableStateFlow(MainUiState())
    val uiState: StateFlow<MainUiState> = _uiState.asStateFlow()
    
    init {
        checkFolderSelection()
    }
    
    /**
     * Check if a folder has been previously selected
     */
    private fun checkFolderSelection() {
        viewModelScope.launch {
            val hasSavedFolder = folderManager.hasSavedFolder()
            _uiState.value = _uiState.value.copy(
                currentScreen = if (hasSavedFolder) Screen.FlagGrid else Screen.FolderPicker,
                isLoading = false
            )
        }
    }
    
    /**
     * Handle folder selection completion
     */
    fun onFolderSelected() {
        _uiState.value = _uiState.value.copy(
            currentScreen = Screen.FlagGrid
        )
    }
    
    /**
     * Handle language selection
     */
    fun onLanguageSelected(language: Language) {
        _uiState.value = _uiState.value.copy(
            currentScreen = Screen.VideoPlayer,
            selectedLanguage = language
        )
    }
    
    /**
     * Handle video completion or back navigation
     */
    fun onVideoCompleted() {
        _uiState.value = _uiState.value.copy(
            currentScreen = Screen.FlagGrid,
            selectedLanguage = null
        )
    }
    
    /**
     * Reset app (clear saved folder)
     */
    fun resetApp() {
        folderManager.clearSavedFolder()
        _uiState.value = _uiState.value.copy(
            currentScreen = Screen.FolderPicker,
            selectedLanguage = null
        )
    }
    
    /**
     * Navigate to settings (folder picker)
     */
    fun navigateToSettings() {
        _uiState.value = _uiState.value.copy(
            currentScreen = Screen.FolderPicker
        )
    }
}

/**
 * UI State data class
 */
data class MainUiState(
    val currentScreen: Screen = Screen.FolderPicker,
    val selectedLanguage: Language? = null,
    val isLoading: Boolean = true
)

/**
 * Screen navigation enum
 */
enum class Screen {
    FolderPicker,
    FlagGrid,
    VideoPlayer
}
