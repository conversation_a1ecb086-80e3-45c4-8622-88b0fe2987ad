package com.falaileh.elmarjo.repository

import android.content.Context
import android.content.SharedPreferences
import android.net.Uri
import androidx.documentfile.provider.DocumentFile
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * Manages folder selection and video file access using Storage Access Framework
 */
class FolderManager(private val context: Context) {
    
    private val prefs: SharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    
    companion object {
        private const val PREFS_NAME = "elmarjo_prefs"
        private const val KEY_FOLDER_URI = "selected_folder_uri"
    }
    
    /**
     * Save the selected folder URI to SharedPreferences
     */
    fun saveFolderUri(uri: Uri) {
        prefs.edit().putString(KEY_FOLDER_URI, uri.toString()).apply()
        
        // Take persistable URI permission
        try {
            context.contentResolver.takePersistableUriPermission(
                uri,
                android.content.Intent.FLAG_GRANT_READ_URI_PERMISSION
            )
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    
    /**
     * Get the saved folder URI from SharedPreferences
     */
    fun getSavedFolderUri(): Uri? {
        val uriString = prefs.getString(KEY_FOLDER_URI, null)
        return if (uriString != null) Uri.parse(uriString) else null
    }
    
    /**
     * Check if a folder has been selected and saved
     */
    fun hasSavedFolder(): Boolean {
        return getSavedFolderUri() != null
    }
    
    /**
     * Clear the saved folder URI
     */
    fun clearSavedFolder() {
        prefs.edit().remove(KEY_FOLDER_URI).apply()
    }
    
    /**
     * Get all video files from the selected folder
     */
    suspend fun getVideoFiles(): List<DocumentFile> = withContext(Dispatchers.IO) {
        val folderUri = getSavedFolderUri() ?: return@withContext emptyList()
        
        try {
            val folder = DocumentFile.fromTreeUri(context, folderUri)
            if (folder != null && folder.exists() && folder.isDirectory) {
                folder.listFiles()
                    .filter { it.isFile && it.name?.endsWith(".mp4", ignoreCase = true) == true }
                    .toList()
            } else {
                emptyList()
            }
        } catch (e: Exception) {
            e.printStackTrace()
            emptyList()
        }
    }
    
    /**
     * Get a specific video file by name
     */
    suspend fun getVideoFile(fileName: String): DocumentFile? = withContext(Dispatchers.IO) {
        val videoFiles = getVideoFiles()
        videoFiles.find { it.name?.equals(fileName, ignoreCase = true) == true }
    }
    
    /**
     * Check if all required video files exist in the folder
     */
    suspend fun validateVideoFiles(requiredFiles: List<String>): ValidationResult = withContext(Dispatchers.IO) {
        val videoFiles = getVideoFiles()
        val existingFileNames = videoFiles.mapNotNull { it.name?.lowercase() }
        
        val missingFiles = requiredFiles.filter { fileName ->
            !existingFileNames.contains(fileName.lowercase())
        }
        
        ValidationResult(
            isValid = missingFiles.isEmpty(),
            missingFiles = missingFiles,
            foundFiles = existingFileNames.size
        )
    }
}

/**
 * Result of video file validation
 */
data class ValidationResult(
    val isValid: Boolean,
    val missingFiles: List<String>,
    val foundFiles: Int
)
