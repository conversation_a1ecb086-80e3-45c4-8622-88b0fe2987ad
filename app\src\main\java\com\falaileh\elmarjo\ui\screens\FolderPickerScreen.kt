package com.falaileh.elmarjo.ui.screens

import android.app.Activity
import android.content.Intent
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.background
import androidx.compose.foundation.focusable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.falaileh.elmarjo.data.LanguageConstants
import com.falaileh.elmarjo.repository.FolderManager
import kotlinx.coroutines.launch

@Composable
fun FolderPickerScreen(
    onFolderSelected: () -> Unit
) {
    val context = LocalContext.current
    val folderManager = remember { FolderManager(context) }
    val scope = rememberCoroutineScope()
    
    var isValidating by remember { mutableStateOf(false) }
    var validationMessage by remember { mutableStateOf("") }
    val focusRequester = remember { FocusRequester() }
    
    // Folder picker launcher
    val folderPickerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            result.data?.data?.let { uri ->
                folderManager.saveFolderUri(uri)
                
                // Validate the selected folder
                scope.launch {
                    isValidating = true
                    val requiredFiles = LanguageConstants.LANGUAGES.map { it.videoFileName }
                    val validation = folderManager.validateVideoFiles(requiredFiles)
                    
                    if (validation.isValid) {
                        validationMessage = "All ${validation.foundFiles} video files found!"
                        kotlinx.coroutines.delay(1500)
                        onFolderSelected()
                    } else {
                        validationMessage = "Missing ${validation.missingFiles.size} files: ${validation.missingFiles.take(3).joinToString(", ")}${if (validation.missingFiles.size > 3) "..." else ""}"
                    }
                    isValidating = false
                }
            }
        }
    }
    
    // Apply 90-degree rotation to the entire screen
    Box(
        modifier = Modifier
            .fillMaxSize()
            .graphicsLayer { rotationZ = 90f }
            .background(Color.Black),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center,
            modifier = Modifier
                .fillMaxSize()
                .padding(32.dp)
        ) {
            // Title
            Text(
                text = "Welcome to Elmarjo",
                fontSize = 32.sp,
                fontWeight = FontWeight.Bold,
                color = Color.White,
                textAlign = TextAlign.Center,
                modifier = Modifier.padding(bottom = 24.dp)
            )
            
            // Instructions
            Text(
                text = "Please select a folder containing your video files\n(22 videos: en.mp4, fr.mp4, ar.mp4, etc.)",
                fontSize = 18.sp,
                color = Color.Gray,
                textAlign = TextAlign.Center,
                modifier = Modifier.padding(bottom = 32.dp)
            )
            
            // Select Folder Button
            Button(
                onClick = {
                    val intent = Intent(Intent.ACTION_OPEN_DOCUMENT_TREE)
                    folderPickerLauncher.launch(intent)
                },
                modifier = Modifier
                    .focusRequester(focusRequester)
                    .focusable()
                    .size(width = 200.dp, height = 60.dp)
                    .clip(RoundedCornerShape(8.dp)),
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.colorScheme.primary
                ),
                enabled = !isValidating
            ) {
                Text(
                    text = if (isValidating) "Validating..." else "Select Folder",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium
                )
            }
            
            // Validation message
            if (validationMessage.isNotEmpty()) {
                Spacer(modifier = Modifier.height(24.dp))
                Text(
                    text = validationMessage,
                    fontSize = 14.sp,
                    color = if (validationMessage.contains("Missing")) Color.Red else Color.Green,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(horizontal = 16.dp)
                )
            }
            
            // Loading indicator
            if (isValidating) {
                Spacer(modifier = Modifier.height(16.dp))
                CircularProgressIndicator(
                    color = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(24.dp)
                )
            }
        }
    }
    
    // Request focus on the button when the screen loads
    LaunchedEffect(Unit) {
        focusRequester.requestFocus()
    }
}
