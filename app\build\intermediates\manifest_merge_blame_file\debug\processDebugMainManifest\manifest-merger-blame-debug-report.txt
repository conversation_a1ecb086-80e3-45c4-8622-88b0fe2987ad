1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.falaileh.elmarjo"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="36" />
10
11    <!-- Permissions for file access and storage -->
12    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
12-->E:\elmarjo\app\src\main\AndroidManifest.xml:6:5-80
12-->E:\elmarjo\app\src\main\AndroidManifest.xml:6:22-77
13    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
13-->E:\elmarjo\app\src\main\AndroidManifest.xml:7:5-8:40
13-->E:\elmarjo\app\src\main\AndroidManifest.xml:7:22-79
14
15    <!-- Android TV support - but keep as regular app -->
16    <uses-feature
16-->E:\elmarjo\app\src\main\AndroidManifest.xml:11:5-13:36
17        android:name="android.software.leanback"
17-->E:\elmarjo\app\src\main\AndroidManifest.xml:12:9-49
18        android:required="false" />
18-->E:\elmarjo\app\src\main\AndroidManifest.xml:13:9-33
19    <uses-feature
19-->E:\elmarjo\app\src\main\AndroidManifest.xml:14:5-16:36
20        android:name="android.hardware.touchscreen"
20-->E:\elmarjo\app\src\main\AndroidManifest.xml:15:9-52
21        android:required="false" />
21-->E:\elmarjo\app\src\main\AndroidManifest.xml:16:9-33
22
23    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
23-->[androidx.media3:media3-common:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f56c78e1e73ea3970bd026892f759526\transformed\media3-common-1.2.1\AndroidManifest.xml:22:5-79
23-->[androidx.media3:media3-common:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f56c78e1e73ea3970bd026892f759526\transformed\media3-common-1.2.1\AndroidManifest.xml:22:22-76
24
25    <permission
25-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0e5df3da8374bed71d9993cb807cb2d\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
26        android:name="com.falaileh.elmarjo.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
26-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0e5df3da8374bed71d9993cb807cb2d\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
27        android:protectionLevel="signature" />
27-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0e5df3da8374bed71d9993cb807cb2d\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
28
29    <uses-permission android:name="com.falaileh.elmarjo.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
29-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0e5df3da8374bed71d9993cb807cb2d\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
29-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0e5df3da8374bed71d9993cb807cb2d\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
30
31    <application
31-->E:\elmarjo\app\src\main\AndroidManifest.xml:18:5-42:19
32        android:allowBackup="true"
32-->E:\elmarjo\app\src\main\AndroidManifest.xml:19:9-35
33        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
33-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0e5df3da8374bed71d9993cb807cb2d\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
34        android:dataExtractionRules="@xml/data_extraction_rules"
34-->E:\elmarjo\app\src\main\AndroidManifest.xml:20:9-65
35        android:debuggable="true"
36        android:extractNativeLibs="false"
37        android:fullBackupContent="@xml/backup_rules"
37-->E:\elmarjo\app\src\main\AndroidManifest.xml:21:9-54
38        android:icon="@mipmap/ic_launcher"
38-->E:\elmarjo\app\src\main\AndroidManifest.xml:22:9-43
39        android:label="@string/app_name"
39-->E:\elmarjo\app\src\main\AndroidManifest.xml:23:9-41
40        android:roundIcon="@mipmap/ic_launcher_round"
40-->E:\elmarjo\app\src\main\AndroidManifest.xml:24:9-54
41        android:supportsRtl="true"
41-->E:\elmarjo\app\src\main\AndroidManifest.xml:25:9-35
42        android:theme="@style/Theme.Elmarjo" >
42-->E:\elmarjo\app\src\main\AndroidManifest.xml:26:9-45
43        <activity
43-->E:\elmarjo\app\src\main\AndroidManifest.xml:27:9-41:20
44            android:name="com.falaileh.elmarjo.MainActivity"
44-->E:\elmarjo\app\src\main\AndroidManifest.xml:28:13-41
45            android:configChanges="orientation|keyboardHidden|screenSize"
45-->E:\elmarjo\app\src\main\AndroidManifest.xml:33:13-74
46            android:exported="true"
46-->E:\elmarjo\app\src\main\AndroidManifest.xml:29:13-36
47            android:label="@string/app_name"
47-->E:\elmarjo\app\src\main\AndroidManifest.xml:30:13-45
48            android:screenOrientation="landscape"
48-->E:\elmarjo\app\src\main\AndroidManifest.xml:32:13-50
49            android:theme="@style/Theme.Elmarjo" >
49-->E:\elmarjo\app\src\main\AndroidManifest.xml:31:13-49
50            <intent-filter>
50-->E:\elmarjo\app\src\main\AndroidManifest.xml:34:13-40:29
51                <action android:name="android.intent.action.MAIN" />
51-->E:\elmarjo\app\src\main\AndroidManifest.xml:35:17-69
51-->E:\elmarjo\app\src\main\AndroidManifest.xml:35:25-66
52
53                <category android:name="android.intent.category.LAUNCHER" />
53-->E:\elmarjo\app\src\main\AndroidManifest.xml:37:17-77
53-->E:\elmarjo\app\src\main\AndroidManifest.xml:37:27-74
54                <!-- Add TV launcher category for Android TV -->
55                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
55-->E:\elmarjo\app\src\main\AndroidManifest.xml:39:17-86
55-->E:\elmarjo\app\src\main\AndroidManifest.xml:39:27-83
56            </intent-filter>
57        </activity>
58
59        <uses-library
59-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5b5d6c589d9f8c1dd2cc72f7d6e8effa\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
60            android:name="androidx.window.extensions"
60-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5b5d6c589d9f8c1dd2cc72f7d6e8effa\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
61            android:required="false" />
61-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5b5d6c589d9f8c1dd2cc72f7d6e8effa\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
62        <uses-library
62-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5b5d6c589d9f8c1dd2cc72f7d6e8effa\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
63            android:name="androidx.window.sidecar"
63-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5b5d6c589d9f8c1dd2cc72f7d6e8effa\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
64            android:required="false" />
64-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5b5d6c589d9f8c1dd2cc72f7d6e8effa\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
65
66        <activity
66-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\368884b31e1d49d62cfc67bcb3df93c5\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
67            android:name="androidx.compose.ui.tooling.PreviewActivity"
67-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\368884b31e1d49d62cfc67bcb3df93c5\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
68            android:exported="true" />
68-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\368884b31e1d49d62cfc67bcb3df93c5\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
69
70        <provider
70-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ae139cc0e8023047d35732112035efe\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
71            android:name="androidx.startup.InitializationProvider"
71-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ae139cc0e8023047d35732112035efe\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
72            android:authorities="com.falaileh.elmarjo.androidx-startup"
72-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ae139cc0e8023047d35732112035efe\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
73            android:exported="false" >
73-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ae139cc0e8023047d35732112035efe\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
74            <meta-data
74-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ae139cc0e8023047d35732112035efe\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
75                android:name="androidx.emoji2.text.EmojiCompatInitializer"
75-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ae139cc0e8023047d35732112035efe\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
76                android:value="androidx.startup" />
76-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ae139cc0e8023047d35732112035efe\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
77            <meta-data
77-->[androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\eacb337cd386f6c749e473d056604a42\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:29:13-31:52
78                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
78-->[androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\eacb337cd386f6c749e473d056604a42\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:30:17-78
79                android:value="androidx.startup" />
79-->[androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\eacb337cd386f6c749e473d056604a42\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:31:17-49
80            <meta-data
80-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
81                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
81-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
82                android:value="androidx.startup" />
82-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
83        </provider>
84
85        <activity
85-->[androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\c756c9cce9b2c5b631b4841f4f3a425a\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:23:9-25:39
86            android:name="androidx.activity.ComponentActivity"
86-->[androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\c756c9cce9b2c5b631b4841f4f3a425a\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:24:13-63
87            android:exported="true" />
87-->[androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\c756c9cce9b2c5b631b4841f4f3a425a\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:25:13-36
88
89        <receiver
89-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
90            android:name="androidx.profileinstaller.ProfileInstallReceiver"
90-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
91            android:directBootAware="false"
91-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
92            android:enabled="true"
92-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
93            android:exported="true"
93-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
94            android:permission="android.permission.DUMP" >
94-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
95            <intent-filter>
95-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
96                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
96-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
96-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
97            </intent-filter>
98            <intent-filter>
98-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
99                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
99-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
99-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
100            </intent-filter>
101            <intent-filter>
101-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
102                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
102-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
102-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
103            </intent-filter>
104            <intent-filter>
104-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
105                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
105-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
105-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e81f3545e2ffd507640b71d4dbe519e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
106            </intent-filter>
107        </receiver>
108    </application>
109
110</manifest>
