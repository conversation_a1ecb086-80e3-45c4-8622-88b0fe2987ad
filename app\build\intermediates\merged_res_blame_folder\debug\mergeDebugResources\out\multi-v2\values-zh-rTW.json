{"logs": [{"outputFile": "com.falaileh.elmarjo.app-mergeDebugResources-65:/values-zh-rTW/values-zh-rTW.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\32ffae0c59a3b7f539124213f72623ec\\transformed\\media3-ui-1.2.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,450,608,689,769,837,915,992,1048,1109,1183,1257,1319,1380,1439,1504,1592,1677,1765,1828,1895,1960,2016,2090,2163,2224,2287,2339,2397,2444,2505,2562,2624,2681,2742,2798,2853,2916,2978,3041,3090,3142,3208,3274", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,80,79,67,77,76,55,60,73,73,61,60,58,64,87,84,87,62,66,64,55,73,72,60,62,51,57,46,60,56,61,56,60,55,54,62,61,62,48,51,65,65,48", "endOffsets": "282,445,603,684,764,832,910,987,1043,1104,1178,1252,1314,1375,1434,1499,1587,1672,1760,1823,1890,1955,2011,2085,2158,2219,2282,2334,2392,2439,2500,2557,2619,2676,2737,2793,2848,2911,2973,3036,3085,3137,3203,3269,3318"}, "to": {"startLines": "2,11,15,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,337,500,4360,4441,4521,4589,4667,4744,4800,4861,4935,5009,5071,5132,5191,5256,5344,5429,5517,5580,5647,5712,5768,5842,5915,5976,6598,6650,6708,6755,6816,6873,6935,6992,7053,7109,7164,7227,7289,7352,7401,7453,7519,7585", "endLines": "10,14,18,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109", "endColumns": "17,12,12,80,79,67,77,76,55,60,73,73,61,60,58,64,87,84,87,62,66,64,55,73,72,60,62,51,57,46,60,56,61,56,60,55,54,62,61,62,48,51,65,65,48", "endOffsets": "332,495,653,4436,4516,4584,4662,4739,4795,4856,4930,5004,5066,5127,5186,5251,5339,5424,5512,5575,5642,5707,5763,5837,5910,5971,6034,6645,6703,6750,6811,6868,6930,6987,7048,7104,7159,7222,7284,7347,7396,7448,7514,7580,7629"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5ae455f1a13db310449e207b49d3bacc\\transformed\\appcompat-1.1.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,833,925,1018,1115,1211,1307,1401,1497,1589,1681,1773,1850,1946,2041,2136,2233,2329,2427,2577,2671", "endColumns": "94,92,99,81,96,107,76,75,91,92,96,95,95,93,95,91,91,91,76,95,94,94,96,95,97,149,93,77", "endOffsets": "195,288,388,470,567,675,752,828,920,1013,1110,1206,1302,1396,1492,1584,1676,1768,1845,1941,2036,2131,2228,2324,2422,2572,2666,2744"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,176", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "658,753,846,946,1028,1125,1233,1310,1386,1478,1571,1668,1764,1860,1954,2050,2142,2234,2326,2403,2499,2594,2689,2786,2882,2980,3130,13889", "endColumns": "94,92,99,81,96,107,76,75,91,92,96,95,95,93,95,91,91,91,76,95,94,94,96,95,97,149,93,77", "endOffsets": "748,841,941,1023,1120,1228,1305,1381,1473,1566,1663,1759,1855,1949,2045,2137,2229,2321,2398,2494,2589,2684,2781,2877,2975,3125,3219,13962"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d0e5df3da8374bed71d9993cb807cb2d\\transformed\\core-1.16.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "46,47,48,49,50,51,52,181", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3224,3316,3415,3509,3603,3696,3789,14251", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "3311,3410,3504,3598,3691,3784,3880,14347"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d5341083af57bd2b42f46054ffafb559\\transformed\\preference-1.2.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,171,252,322,442,610,689", "endColumns": "65,80,69,119,167,78,75", "endOffsets": "166,247,317,437,605,684,760"}, "to": {"startLines": "55,110,172,173,182,188,189", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4038,7634,13551,13621,14352,14940,15019", "endColumns": "65,80,69,119,167,78,75", "endOffsets": "4099,7710,13616,13736,14515,15014,15090"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e754f461ad8f7a511fa7fc6c4385c693\\transformed\\material3-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,159,262,366,468,560,648,752,857,962,1078,1160,1256,1340,1428,1533,1646,1747,1856,1963,2071,2188,2293,2394,2498,2603,2688,2783,2888,2997,3087,3187,3285,3396,3512,3612,3703,3777,3867,3956,4048,4131,4213,4302,4382,4464,4561,4655,4748,4841,4925,5022,5118,5213,5321,5401,5495", "endColumns": "103,102,103,101,91,87,103,104,104,115,81,95,83,87,104,112,100,108,106,107,116,104,100,103,104,84,94,104,108,89,99,97,110,115,99,90,73,89,88,91,82,81,88,79,81,96,93,92,92,83,96,95,94,107,79,93,91", "endOffsets": "154,257,361,463,555,643,747,852,957,1073,1155,1251,1335,1423,1528,1641,1742,1851,1958,2066,2183,2288,2389,2493,2598,2683,2778,2883,2992,3082,3182,3280,3391,3507,3607,3698,3772,3862,3951,4043,4126,4208,4297,4377,4459,4556,4650,4743,4836,4920,5017,5113,5208,5316,5396,5490,5582"}, "to": {"startLines": "113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7866,7970,8073,8177,8279,8371,8459,8563,8668,8773,8889,8971,9067,9151,9239,9344,9457,9558,9667,9774,9882,9999,10104,10205,10309,10414,10499,10594,10699,10808,10898,10998,11096,11207,11323,11423,11514,11588,11678,11767,11859,11942,12024,12113,12193,12275,12372,12466,12559,12652,12736,12833,12929,13024,13132,13212,13306", "endColumns": "103,102,103,101,91,87,103,104,104,115,81,95,83,87,104,112,100,108,106,107,116,104,100,103,104,84,94,104,108,89,99,97,110,115,99,90,73,89,88,91,82,81,88,79,81,96,93,92,92,83,96,95,94,107,79,93,91", "endOffsets": "7965,8068,8172,8274,8366,8454,8558,8663,8768,8884,8966,9062,9146,9234,9339,9452,9553,9662,9769,9877,9994,10099,10200,10304,10409,10494,10589,10694,10803,10893,10993,11091,11202,11318,11418,11509,11583,11673,11762,11854,11937,12019,12108,12188,12270,12367,12461,12554,12647,12731,12828,12924,13019,13127,13207,13301,13393"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\eb5978be55e4593326b90344069d1b2d\\transformed\\ui-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,182,258,345,436,514,588,665,743,818,891,966,1034,1107,1179,1250,1324,1392", "endColumns": "76,75,86,90,77,73,76,77,74,72,74,67,72,71,70,73,67,115", "endOffsets": "177,253,340,431,509,583,660,738,813,886,961,1029,1102,1174,1245,1319,1387,1503"}, "to": {"startLines": "53,54,56,57,58,111,112,170,171,174,175,177,178,179,180,183,184,185", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3885,3962,4104,4191,4282,7715,7789,13398,13476,13741,13814,13967,14035,14108,14180,14520,14594,14662", "endColumns": "76,75,86,90,77,73,76,77,74,72,74,67,72,71,70,73,67,115", "endOffsets": "3957,4033,4186,4277,4355,7784,7861,13471,13546,13809,13884,14030,14103,14175,14246,14589,14657,14773"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5510098f7f655104979ce51ebf04d4e9\\transformed\\media3-exoplayer-1.2.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,111,167,224,277,347,401,477,555", "endColumns": "55,55,56,52,69,53,75,77,58", "endOffsets": "106,162,219,272,342,396,472,550,609"}, "to": {"startLines": "83,84,85,86,87,88,89,90,91", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6039,6095,6151,6208,6261,6331,6385,6461,6539", "endColumns": "55,55,56,52,69,53,75,77,58", "endOffsets": "6090,6146,6203,6256,6326,6380,6456,6534,6593"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\3109e90bbcb133a6123d6a171e482e61\\transformed\\foundation-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,138", "endColumns": "82,78", "endOffsets": "133,212"}, "to": {"startLines": "186,187", "startColumns": "4,4", "startOffsets": "14778,14861", "endColumns": "82,78", "endOffsets": "14856,14935"}}]}]}