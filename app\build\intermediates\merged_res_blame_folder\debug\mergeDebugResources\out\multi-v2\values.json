{"logs": [{"outputFile": "com.falaileh.elmarjo.app-mergeDebugResources-65:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9cd3daaaa7b2a32f64013d0258d8a30a\\transformed\\fragment-1.3.6\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "473,496,527,3419,3424", "startColumns": "4,4,4,4,4", "startOffsets": "29102,30264,31896,196279,196449", "endLines": "473,496,527,3423,3427", "endColumns": "56,64,63,24,24", "endOffsets": "29154,30324,31955,196444,196593"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\750ad70ca7d2f2aadf98cdad3b4c7def\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "523", "startColumns": "4", "startOffsets": "31689", "endColumns": "42", "endOffsets": "31727"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\33a4c7d8d3b2c20bccb91c8c29de66dd\\transformed\\recyclerview-1.3.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,111,170,218,274,349,425,497,563", "endLines": "2,3,4,5,6,7,8,9,30", "endColumns": "55,58,47,55,74,75,71,65,24", "endOffsets": "106,165,213,269,344,420,492,558,1398"}, "to": {"startLines": "39,301,302,303,311,312,313,481,3976", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "1668,17849,17908,17956,18623,18698,18774,29506,214583", "endLines": "39,301,302,303,311,312,313,481,3996", "endColumns": "55,58,47,55,74,75,71,65,24", "endOffsets": "1719,17903,17951,18007,18693,18769,18841,29567,215418"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d5341083af57bd2b42f46054ffafb559\\transformed\\preference-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,22,23,24,25,42,45,51,57,60,66,70,73,80,86,89,95,100,105,112,114,120,126,134,139,146,151,157,161,168,172,178,184,187,192,193,194,199,215,238,243,257,268,348,358,368,386,392,439,461,485", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,178,247,311,366,434,501,566,623,680,728,776,837,900,963,1001,1058,1102,1242,1381,1431,1479,2917,3022,3378,3716,3862,4202,4414,4577,4984,5322,5445,5784,6023,6280,6651,6711,7049,7335,7784,8076,8464,8769,9113,9358,9688,9895,10163,10436,10580,10949,10996,11052,11308,12367,13788,14126,15012,15622,20168,20687,21229,22503,22763,25467,26989,28470", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,21,22,23,24,41,44,50,56,59,65,69,72,79,85,88,94,99,104,111,113,119,125,133,138,145,150,156,160,167,171,177,183,186,191,192,193,198,214,237,242,256,267,347,357,367,385,391,438,460,484,508", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "173,242,306,361,429,496,561,618,675,723,771,832,895,958,996,1053,1097,1237,1376,1426,1474,2912,3017,3373,3711,3857,4197,4409,4572,4979,5317,5440,5779,6018,6275,6646,6706,7044,7330,7779,8071,8459,8764,9108,9353,9683,9890,10158,10431,10575,10944,10991,11047,11303,12362,13783,14121,15007,15617,20163,20682,21224,22498,22758,25462,26984,28465,29984"}, "to": {"startLines": "95,152,329,330,331,332,333,334,335,488,489,490,536,537,585,642,708,709,720,726,727,1800,2166,2169,2175,2181,2184,2190,2194,2197,2204,2210,2213,2219,2224,2229,2236,2238,2244,2250,2258,2263,2270,2275,2281,2285,2292,2296,2302,2308,2311,2315,2316,3241,3256,3321,3359,3529,3704,3830,3894,3904,3914,3921,3927,4032,4171,4188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4174,8049,19824,19888,19943,20011,20078,20143,20200,29867,29915,29963,32485,32548,35872,40239,44965,45009,45551,45974,46024,114230,138316,138421,138666,139004,139150,139490,139702,139865,140272,140610,140733,141072,141311,141568,141939,141999,142337,142623,143072,143364,143752,144057,144401,144646,144976,145183,145451,145724,145868,146069,146116,189184,189707,192342,193643,199605,205200,209911,211836,212118,212423,212685,212945,216506,221865,222395", "endLines": "95,152,329,330,331,332,333,334,335,488,489,490,536,537,585,642,708,711,720,726,727,1816,2168,2174,2180,2183,2189,2193,2196,2203,2209,2212,2218,2223,2228,2235,2237,2243,2249,2257,2262,2269,2274,2280,2284,2291,2295,2301,2307,2310,2314,2315,2316,3245,3266,3340,3362,3538,3711,3893,3903,3913,3920,3926,3969,4044,4187,4204", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "4242,8113,19883,19938,20006,20073,20138,20195,20252,29910,29958,30019,32543,32606,35905,40291,45004,45144,45685,46019,46067,115663,138416,138661,138999,139145,139485,139697,139860,140267,140605,140728,141067,141306,141563,141934,141994,142332,142618,143067,143359,143747,144052,144396,144641,144971,145178,145446,145719,145863,146064,146111,146167,189364,190103,193066,193787,199932,205443,211831,212113,212418,212680,212940,214363,216953,222390,222958"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\45e9841bf14352b5cf55a50ccc4c4f0b\\transformed\\navigation-common-2.7.6\\res\\values\\values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "3712,3725,3731,3737,3746", "startColumns": "4,4,4,4,4", "startOffsets": "205448,206087,206331,206578,206941", "endLines": "3724,3730,3736,3739,3750", "endColumns": "24,24,24,24,24", "endOffsets": "206082,206326,206573,206706,207118"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7b41efae23e7ab51887be8f583b8951e\\transformed\\navigation-runtime-2.7.6\\res\\values\\values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "485,2699,3740,3743", "startColumns": "4,4,4,4", "startOffsets": "29704,171465,206711,206826", "endLines": "485,2705,3742,3745", "endColumns": "52,24,24,24", "endOffsets": "29752,171764,206821,206936"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af483ecb090730046da5fb2d3b4f1c85\\transformed\\media-1.6.0\\res\\values\\values.xml", "from": {"startLines": "2,5,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,288,410,476,598,659,725", "endColumns": "88,61,65,121,60,65,66", "endOffsets": "139,345,471,593,654,720,787"}, "to": {"startLines": "151,484,2433,2435,2436,2441,2443", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "7960,29642,155459,155635,155757,156019,156214", "endColumns": "88,61,65,121,60,65,66", "endOffsets": "8044,29699,155520,155752,155813,156080,156276"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\59782c46e92902f9e632069eb2572923\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "574", "startColumns": "4", "startOffsets": "35114", "endColumns": "82", "endOffsets": "35192"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9fcdb86bfab48733c738bc96cd27f5ed\\transformed\\appcompat-resources-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,29,36,47,74", "startColumns": "4,4,4,4,4", "startOffsets": "55,1702,2087,2684,4317", "endLines": "28,35,46,73,78", "endColumns": "24,24,24,24,24", "endOffsets": "1697,2082,2679,4312,4582"}, "to": {"startLines": "2720,2736,2742,4085,4101", "startColumns": "4,4,4,4,4", "startOffsets": "172417,172842,173020,218301,218712", "endLines": "2735,2741,2751,4100,4104", "endColumns": "24,24,24,24,24", "endOffsets": "172837,173015,173299,218707,218834"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\eb5978be55e4593326b90344069d1b2d\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,61,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3028,3082,3128,3174,3216,3256,3303,3339,3429,3541,3652", "endLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63,67", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3023,3077,3123,3169,3211,3251,3298,3334,3424,3536,3647,3842"}, "to": {"startLines": "427,429,430,477,479,528,583,584,586,587,588,643,644,706,707,712,713,715,716,717,718,721,722,723,1817,1999,2002", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "26976,27110,27168,29312,29397,31960,35753,35818,35910,35976,36077,40296,40348,44849,44911,45149,45199,45306,45352,45398,45440,45690,45737,45773,115668,126060,126171", "endLines": "427,429,430,477,479,528,583,584,586,587,588,643,644,706,707,712,713,715,716,717,718,721,722,723,1819,2001,2005", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "27045,27163,27218,29358,29447,32008,35813,35867,35971,36072,36130,40343,40403,44906,44960,45194,45248,45347,45393,45435,45475,45732,45768,45858,115775,126166,126361"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5b5d6c589d9f8c1dd2cc72f7d6e8effa\\transformed\\window-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,7,8,9,10,11,19,23,34,51", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,234,294,346,391,451,869,1066,1790,2904", "endLines": "6,7,8,9,10,18,22,33,50,58", "endColumns": "11,59,51,44,59,24,24,24,24,24", "endOffsets": "229,289,341,386,446,864,1061,1785,2899,3292"}, "to": {"startLines": "71,76,77,78,428,2693,2706,4057,4065,4077", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "3063,3242,3302,3354,27050,171270,171769,217270,217552,217992", "endLines": "75,76,77,78,428,2698,2709,4064,4076,4084", "endColumns": "11,59,51,44,59,24,24,24,24,24", "endOffsets": "3237,3297,3349,3394,27105,171460,171895,217547,217987,218296"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6f9fffeca9b4e75d900913b18ac2df78\\transformed\\core-viewtree-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "522", "startColumns": "4", "startOffsets": "31646", "endColumns": "42", "endOffsets": "31684"}}, {"source": "E:\\elmarjo\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "7,2,3,4,5,6,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "286,55,102,149,196,241,328", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "323,97,144,191,236,281,365"}, "to": {"startLines": "108,161,162,163,174,175,178", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5174,8706,8753,8800,9544,9589,9755", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "5211,8748,8795,8842,9584,9629,9792"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\3109e90bbcb133a6123d6a171e482e61\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,111", "endColumns": "55,54", "endOffsets": "106,161"}, "to": {"startLines": "724,725", "startColumns": "4,4", "startOffsets": "45863,45919", "endColumns": "55,54", "endOffsets": "45914,45969"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5ae455f1a13db310449e207b49d3bacc\\transformed\\appcompat-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,218,219,223,227,231,236,242,249,253,257,262,266,270,274,278,282,286,292,296,302,306,312,316,321,325,328,332,338,342,348,352,358,361,365,369,373,377,381,382,383,384,387,390,393,396,400,401,402,403,404,407,409,411,413,418,419,423,429,433,434,436,447,448,452,458,462,463,464,468,495,499,500,504,532,703,729,901,927,958,966,972,986,1008,1013,1018,1028,1037,1046,1050,1057,1065,1072,1073,1082,1085,1088,1092,1096,1100,1103,1104,1109,1114,1124,1129,1136,1142,1143,1146,1150,1155,1157,1159,1162,1165,1167,1171,1174,1181,1184,1187,1191,1193,1197,1199,1201,1203,1207,1215,1223,1235,1241,1250,1253,1264,1267,1268,1273,1274,1279,1348,1418,1419,1429,1438,1439,1441,1445,1448,1451,1454,1457,1460,1463,1466,1470,1473,1476,1479,1483,1486,1490,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1516,1518,1519,1520,1521,1522,1523,1524,1525,1527,1528,1530,1531,1533,1535,1536,1538,1539,1540,1541,1542,1543,1545,1546,1547,1548,1549,1550,1552,1554,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1570,1571,1572,1573,1574,1575,1577,1581,1585,1586,1587,1588,1589,1590,1594,1595,1596,1597,1599,1601,1603,1605,1607,1608,1609,1610,1612,1614,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1630,1631,1632,1633,1635,1637,1638,1640,1641,1643,1645,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1658,1660,1661,1662,1663,1665,1666,1667,1668,1669,1671,1673,1675,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1775,1778,1781,1784,1798,1809,1819,1849,1876,1885,1960,2357,2362,2390,2408,2444,2450,2456,2479,2620,2640,2646,2650,2656,2693,2705,2771,2795,2864,2883,2909", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,409,471,552,613,688,764,841,919,1004,1086,1162,1238,1315,1393,1499,1605,1684,1764,1821,1879,1953,2028,2093,2159,2219,2280,2352,2425,2492,2560,2619,2678,2737,2796,2855,2909,2963,3016,3070,3124,3178,3232,3306,3385,3458,3532,3603,3675,3747,3820,3877,3935,4008,4082,4156,4231,4303,4376,4446,4517,4577,4638,4707,4776,4846,4920,4996,5060,5137,5213,5290,5355,5424,5501,5576,5645,5713,5790,5856,5917,6014,6079,6148,6247,6318,6377,6435,6492,6551,6615,6686,6758,6830,6902,6974,7041,7109,7177,7236,7299,7363,7453,7544,7604,7670,7737,7803,7873,7937,7990,8057,8118,8185,8298,8356,8419,8484,8549,8624,8697,8769,8818,8879,8940,9001,9063,9127,9191,9255,9320,9383,9443,9504,9570,9629,9689,9751,9822,9882,9950,10036,10123,10213,10300,10388,10470,10553,10643,10734,10786,10844,10889,10955,11019,11076,11133,11187,11244,11292,11341,11392,11426,11473,11522,11568,11600,11664,11726,11786,11843,11917,11987,12065,12119,12189,12274,12322,12368,12429,12492,12558,12622,12693,12756,12821,12885,12946,13007,13059,13132,13206,13275,13350,13424,13498,13639,13709,13762,13840,13930,14018,14114,14204,14786,14875,15122,15403,15655,15940,16333,16810,17032,17254,17530,17757,17987,18217,18447,18677,18904,19323,19549,19974,20204,20632,20851,21134,21342,21473,21700,22126,22351,22778,22999,23424,23544,23820,24121,24445,24736,25050,25187,25318,25423,25665,25832,26036,26244,26515,26627,26739,26844,26961,27175,27321,27461,27547,27895,27983,28229,28647,28896,28978,29076,29693,29793,30045,30469,30724,30818,30907,31144,33196,33438,33540,33793,35977,47101,48617,59840,61368,63125,63751,64171,65232,66497,66753,66989,67536,68030,68635,68833,69413,69977,70352,70470,71008,71165,71361,71634,71890,72060,72201,72265,72630,72997,73673,73937,74275,74628,74722,74908,75214,75476,75601,75728,75967,76178,76297,76490,76667,77122,77303,77425,77684,77797,77984,78086,78193,78322,78597,79105,79601,80478,80772,81342,81491,82223,82395,82479,82815,82907,83185,88594,94146,94208,94838,95452,95543,95656,95885,96045,96197,96368,96534,96703,96870,97033,97276,97446,97619,97790,98064,98263,98468,98798,98882,98978,99074,99172,99272,99374,99476,99578,99680,99782,99882,99978,100090,100219,100342,100473,100604,100702,100816,100910,101050,101184,101280,101392,101492,101608,101704,101816,101916,102056,102192,102356,102486,102644,102794,102935,103079,103214,103326,103476,103604,103732,103868,104000,104130,104260,104372,104512,104658,104802,104940,105006,105096,105172,105276,105366,105468,105576,105684,105784,105864,105956,106054,106164,106242,106348,106440,106544,106654,106776,106939,107096,107176,107276,107366,107476,107566,107807,107901,108007,108099,108199,108311,108425,108541,108657,108751,108865,108977,109079,109199,109321,109403,109507,109627,109753,109851,109945,110033,110145,110261,110383,110495,110670,110786,110872,110964,111076,111200,111267,111393,111461,111589,111733,111861,111930,112025,112140,112253,112352,112461,112572,112683,112784,112889,112989,113119,113210,113333,113427,113539,113625,113729,113825,113913,114031,114135,114239,114365,114453,114561,114661,114751,114861,114945,115047,115131,115185,115249,115355,115441,115551,115635,115755,120899,121017,121132,121264,121979,122671,123188,124787,126320,126708,131443,151705,151965,153475,154508,156521,156783,157139,157969,164751,165885,166179,166402,166729,168779,169427,173278,174480,178559,179774,181183", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,217,218,222,226,230,235,241,248,252,256,261,265,269,273,277,281,285,291,295,301,305,311,315,320,324,327,331,337,341,347,351,357,360,364,368,372,376,380,381,382,383,386,389,392,395,399,400,401,402,403,406,408,410,412,417,418,422,428,432,433,435,446,447,451,457,461,462,463,467,494,498,499,503,531,702,728,900,926,957,965,971,985,1007,1012,1017,1027,1036,1045,1049,1056,1064,1071,1072,1081,1084,1087,1091,1095,1099,1102,1103,1108,1113,1123,1128,1135,1141,1142,1145,1149,1154,1156,1158,1161,1164,1166,1170,1173,1180,1183,1186,1190,1192,1196,1198,1200,1202,1206,1214,1222,1234,1240,1249,1252,1263,1266,1267,1272,1273,1278,1347,1417,1418,1428,1437,1438,1440,1444,1447,1450,1453,1456,1459,1462,1465,1469,1472,1475,1478,1482,1485,1489,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1515,1517,1518,1519,1520,1521,1522,1523,1524,1526,1527,1529,1530,1532,1534,1535,1537,1538,1539,1540,1541,1542,1544,1545,1546,1547,1548,1549,1551,1553,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1569,1570,1571,1572,1573,1574,1576,1580,1584,1585,1586,1587,1588,1589,1593,1594,1595,1596,1598,1600,1602,1604,1606,1607,1608,1609,1611,1613,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1629,1630,1631,1632,1634,1636,1637,1639,1640,1642,1644,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1659,1660,1661,1662,1664,1665,1666,1667,1668,1670,1672,1674,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1774,1777,1780,1783,1797,1808,1818,1848,1875,1884,1959,2356,2361,2389,2407,2443,2449,2455,2478,2619,2639,2645,2649,2655,2692,2704,2770,2794,2863,2882,2908,2917", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,404,466,547,608,683,759,836,914,999,1081,1157,1233,1310,1388,1494,1600,1679,1759,1816,1874,1948,2023,2088,2154,2214,2275,2347,2420,2487,2555,2614,2673,2732,2791,2850,2904,2958,3011,3065,3119,3173,3227,3301,3380,3453,3527,3598,3670,3742,3815,3872,3930,4003,4077,4151,4226,4298,4371,4441,4512,4572,4633,4702,4771,4841,4915,4991,5055,5132,5208,5285,5350,5419,5496,5571,5640,5708,5785,5851,5912,6009,6074,6143,6242,6313,6372,6430,6487,6546,6610,6681,6753,6825,6897,6969,7036,7104,7172,7231,7294,7358,7448,7539,7599,7665,7732,7798,7868,7932,7985,8052,8113,8180,8293,8351,8414,8479,8544,8619,8692,8764,8813,8874,8935,8996,9058,9122,9186,9250,9315,9378,9438,9499,9565,9624,9684,9746,9817,9877,9945,10031,10118,10208,10295,10383,10465,10548,10638,10729,10781,10839,10884,10950,11014,11071,11128,11182,11239,11287,11336,11387,11421,11468,11517,11563,11595,11659,11721,11781,11838,11912,11982,12060,12114,12184,12269,12317,12363,12424,12487,12553,12617,12688,12751,12816,12880,12941,13002,13054,13127,13201,13270,13345,13419,13493,13634,13704,13757,13835,13925,14013,14109,14199,14781,14870,15117,15398,15650,15935,16328,16805,17027,17249,17525,17752,17982,18212,18442,18672,18899,19318,19544,19969,20199,20627,20846,21129,21337,21468,21695,22121,22346,22773,22994,23419,23539,23815,24116,24440,24731,25045,25182,25313,25418,25660,25827,26031,26239,26510,26622,26734,26839,26956,27170,27316,27456,27542,27890,27978,28224,28642,28891,28973,29071,29688,29788,30040,30464,30719,30813,30902,31139,33191,33433,33535,33788,35972,47096,48612,59835,61363,63120,63746,64166,65227,66492,66748,66984,67531,68025,68630,68828,69408,69972,70347,70465,71003,71160,71356,71629,71885,72055,72196,72260,72625,72992,73668,73932,74270,74623,74717,74903,75209,75471,75596,75723,75962,76173,76292,76485,76662,77117,77298,77420,77679,77792,77979,78081,78188,78317,78592,79100,79596,80473,80767,81337,81486,82218,82390,82474,82810,82902,83180,88589,94141,94203,94833,95447,95538,95651,95880,96040,96192,96363,96529,96698,96865,97028,97271,97441,97614,97785,98059,98258,98463,98793,98877,98973,99069,99167,99267,99369,99471,99573,99675,99777,99877,99973,100085,100214,100337,100468,100599,100697,100811,100905,101045,101179,101275,101387,101487,101603,101699,101811,101911,102051,102187,102351,102481,102639,102789,102930,103074,103209,103321,103471,103599,103727,103863,103995,104125,104255,104367,104507,104653,104797,104935,105001,105091,105167,105271,105361,105463,105571,105679,105779,105859,105951,106049,106159,106237,106343,106435,106539,106649,106771,106934,107091,107171,107271,107361,107471,107561,107802,107896,108002,108094,108194,108306,108420,108536,108652,108746,108860,108972,109074,109194,109316,109398,109502,109622,109748,109846,109940,110028,110140,110256,110378,110490,110665,110781,110867,110959,111071,111195,111262,111388,111456,111584,111728,111856,111925,112020,112135,112248,112347,112456,112567,112678,112779,112884,112984,113114,113205,113328,113422,113534,113620,113724,113820,113908,114026,114130,114234,114360,114448,114556,114656,114746,114856,114940,115042,115126,115180,115244,115350,115436,115546,115630,115750,120894,121012,121127,121259,121974,122666,123183,124782,126315,126703,131438,151700,151960,153470,154503,156516,156778,157134,157964,164746,165880,166174,166397,166724,168774,169422,173273,174475,178554,179769,181178,181652"}, "to": {"startLines": "29,30,33,87,92,93,94,96,97,98,99,100,101,104,105,106,107,109,110,111,112,113,114,115,116,119,120,121,122,123,124,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,153,154,155,156,157,158,159,160,164,165,166,167,168,169,170,171,172,173,176,177,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,264,265,304,305,306,307,308,309,310,336,337,338,339,340,341,342,343,423,424,425,426,478,491,492,497,521,529,530,531,532,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,714,728,729,730,731,732,733,741,742,746,750,754,759,765,772,776,780,785,789,793,797,801,805,809,815,819,825,829,835,839,844,848,851,855,861,865,871,875,881,884,888,892,896,900,904,905,906,907,910,913,916,919,923,924,925,926,927,930,932,934,936,941,942,946,952,956,957,959,970,971,975,981,985,986,987,991,1018,1022,1023,1027,1055,1225,1251,1422,1448,1479,1487,1493,1507,1529,1534,1539,1549,1558,1567,1571,1578,1586,1593,1594,1603,1606,1609,1613,1617,1621,1624,1625,1630,1635,1645,1650,1657,1663,1664,1667,1671,1676,1678,1680,1683,1686,1688,1692,1695,1702,1705,1708,1712,1714,1718,1720,1722,1724,1728,1736,1744,1756,1762,1771,1774,1785,1788,1789,1794,1795,2006,2075,2145,2146,2156,2165,2317,2319,2323,2326,2329,2332,2335,2338,2341,2344,2348,2351,2354,2357,2361,2364,2368,2372,2373,2374,2375,2376,2377,2378,2379,2380,2381,2382,2383,2384,2385,2386,2387,2388,2389,2390,2391,2392,2394,2396,2397,2398,2399,2400,2401,2402,2403,2405,2406,2408,2409,2411,2413,2414,2416,2417,2418,2419,2420,2421,2423,2424,2425,2426,2427,2444,2446,2448,2450,2451,2452,2453,2454,2455,2456,2457,2458,2459,2460,2461,2462,2464,2465,2466,2467,2468,2469,2471,2475,2480,2481,2482,2483,2484,2485,2489,2490,2491,2492,2494,2496,2498,2500,2502,2503,2504,2505,2507,2509,2511,2512,2513,2514,2515,2516,2517,2518,2519,2520,2521,2522,2525,2526,2527,2528,2530,2532,2533,2535,2536,2538,2540,2542,2543,2544,2545,2546,2547,2548,2549,2550,2551,2552,2553,2555,2556,2557,2558,2560,2561,2562,2563,2564,2566,2568,2570,2572,2573,2574,2575,2576,2577,2578,2579,2580,2581,2582,2583,2584,2585,2586,2589,2664,2667,2670,2673,2687,2710,2752,2781,2808,2817,2879,3246,3277,3341,3493,3517,3523,3539,3560,3684,3820,3826,3970,3997,4045,4105,4205,4225,4280,4292,4318", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1133,1188,1335,3751,3998,4053,4112,4247,4328,4389,4464,4540,4617,4855,4940,5022,5098,5216,5293,5371,5477,5583,5662,5742,5799,5988,6062,6137,6202,6268,6328,6876,6948,7021,7088,7156,7215,7274,7333,7392,7451,7505,7559,7612,7666,7720,7774,8118,8192,8271,8344,8418,8489,8561,8633,8847,8904,8962,9035,9109,9183,9258,9330,9403,9473,9634,9694,9797,9866,9935,10005,10079,10155,10219,10296,10372,10449,10514,10583,10660,10735,10804,10872,10949,11015,11076,11173,11238,11307,11406,11477,11536,11594,11651,11710,11774,11845,11917,11989,12061,12133,12200,12268,12336,12395,12458,12522,12612,12703,12763,12829,12896,12962,13032,13096,13149,13216,13277,13344,13457,13515,13578,13643,13708,13783,13856,13928,13977,14038,14099,14160,14222,14286,14350,14414,14479,14542,14602,14663,14729,14788,14848,14910,14981,15041,15597,15683,18012,18102,18189,18277,18359,18442,18532,20257,20309,20367,20412,20478,20542,20599,20656,26771,26828,26876,26925,29363,30024,30071,30329,31614,32013,32077,32139,32199,33248,33322,33392,33470,33524,33594,33679,33727,33773,33834,33897,33963,34027,34098,34161,34226,34290,34351,34412,34464,34537,34611,34680,34755,34829,34903,35044,45253,46072,46150,46240,46328,46424,46514,47096,47185,47432,47713,47965,48250,48643,49120,49342,49564,49840,50067,50297,50527,50757,50987,51214,51633,51859,52284,52514,52942,53161,53444,53652,53783,54010,54436,54661,55088,55309,55734,55854,56130,56431,56755,57046,57360,57497,57628,57733,57975,58142,58346,58554,58825,58937,59049,59154,59271,59485,59631,59771,59857,60205,60293,60539,60957,61206,61288,61386,61978,62078,62330,62754,63009,63103,63192,63429,65453,65695,65797,66050,68206,78738,80254,90885,92413,94170,94796,95216,96277,97542,97798,98034,98581,99075,99680,99878,100458,101022,101397,101515,102053,102210,102406,102679,102935,103105,103246,103310,103675,104042,104718,104982,105320,105673,105767,105953,106259,106521,106646,106773,107012,107223,107342,107535,107712,108167,108348,108470,108729,108842,109029,109131,109238,109367,109642,110150,110646,111523,111817,112387,112536,113268,113440,113524,113860,113952,126366,131612,137001,137063,137641,138225,146172,146285,146514,146674,146826,146997,147163,147332,147499,147662,147905,148075,148248,148419,148693,148892,149097,149427,149511,149607,149703,149801,149901,150003,150105,150207,150309,150411,150511,150607,150719,150848,150971,151102,151233,151331,151445,151539,151679,151813,151909,152021,152121,152237,152333,152445,152545,152685,152821,152985,153115,153273,153423,153564,153708,153843,153955,154105,154233,154361,154497,154629,154759,154889,155001,156281,156427,156571,156709,156775,156865,156941,157045,157135,157237,157345,157453,157553,157633,157725,157823,157933,158011,158117,158209,158313,158423,158545,158708,158949,159029,159129,159219,159329,159419,159660,159754,159860,159952,160052,160164,160278,160394,160510,160604,160718,160830,160932,161052,161174,161256,161360,161480,161606,161704,161798,161886,161998,162114,162236,162348,162523,162639,162725,162817,162929,163053,163120,163246,163314,163442,163586,163714,163783,163878,163993,164106,164205,164314,164425,164536,164637,164742,164842,164972,165063,165186,165280,165392,165478,165582,165678,165766,165884,165988,166092,166218,166306,166414,166514,166604,166714,166798,166900,166984,167038,167102,167208,167294,167404,167488,167747,170363,170481,170596,170676,171037,171900,173304,174648,176009,176397,179172,189369,190409,193071,198392,199143,199405,199937,200316,204594,209531,209760,214368,215423,216958,218839,222963,223707,225838,226178,227489", "endLines": "29,30,33,87,92,93,94,96,97,98,99,100,101,104,105,106,107,109,110,111,112,113,114,115,116,119,120,121,122,123,124,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,153,154,155,156,157,158,159,160,164,165,166,167,168,169,170,171,172,173,176,177,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,264,265,304,305,306,307,308,309,310,336,337,338,339,340,341,342,343,423,424,425,426,478,491,492,497,521,529,530,531,532,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,714,728,729,730,731,732,740,741,745,749,753,758,764,771,775,779,784,788,792,796,800,804,808,814,818,824,828,834,838,843,847,850,854,860,864,870,874,880,883,887,891,895,899,903,904,905,906,909,912,915,918,922,923,924,925,926,929,931,933,935,940,941,945,951,955,956,958,969,970,974,980,984,985,986,990,1017,1021,1022,1026,1054,1224,1250,1421,1447,1478,1486,1492,1506,1528,1533,1538,1548,1557,1566,1570,1577,1585,1592,1593,1602,1605,1608,1612,1616,1620,1623,1624,1629,1634,1644,1649,1656,1662,1663,1666,1670,1675,1677,1679,1682,1685,1687,1691,1694,1701,1704,1707,1711,1713,1717,1719,1721,1723,1727,1735,1743,1755,1761,1770,1773,1784,1787,1788,1793,1794,1799,2074,2144,2145,2155,2164,2165,2318,2322,2325,2328,2331,2334,2337,2340,2343,2347,2350,2353,2356,2360,2363,2367,2371,2372,2373,2374,2375,2376,2377,2378,2379,2380,2381,2382,2383,2384,2385,2386,2387,2388,2389,2390,2391,2393,2395,2396,2397,2398,2399,2400,2401,2402,2404,2405,2407,2408,2410,2412,2413,2415,2416,2417,2418,2419,2420,2422,2423,2424,2425,2426,2427,2445,2447,2449,2450,2451,2452,2453,2454,2455,2456,2457,2458,2459,2460,2461,2463,2464,2465,2466,2467,2468,2470,2474,2478,2480,2481,2482,2483,2484,2488,2489,2490,2491,2493,2495,2497,2499,2501,2502,2503,2504,2506,2508,2510,2511,2512,2513,2514,2515,2516,2517,2518,2519,2520,2521,2524,2525,2526,2527,2529,2531,2532,2534,2535,2537,2539,2541,2542,2543,2544,2545,2546,2547,2548,2549,2550,2551,2552,2554,2555,2556,2557,2559,2560,2561,2562,2563,2565,2567,2569,2571,2572,2573,2574,2575,2576,2577,2578,2579,2580,2581,2582,2583,2584,2585,2586,2663,2666,2669,2672,2686,2692,2719,2780,2807,2816,2878,3237,3249,3304,3358,3516,3522,3528,3559,3683,3703,3825,3829,3975,4031,4056,4170,4224,4279,4291,4317,4324", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "1183,1228,1379,3787,4048,4107,4169,4323,4384,4459,4535,4612,4690,4935,5017,5093,5169,5288,5366,5472,5578,5657,5737,5794,5852,6057,6132,6197,6263,6323,6384,6943,7016,7083,7151,7210,7269,7328,7387,7446,7500,7554,7607,7661,7715,7769,7823,8187,8266,8339,8413,8484,8556,8628,8701,8899,8957,9030,9104,9178,9253,9325,9398,9468,9539,9689,9750,9861,9930,10000,10074,10150,10214,10291,10367,10444,10509,10578,10655,10730,10799,10867,10944,11010,11071,11168,11233,11302,11401,11472,11531,11589,11646,11705,11769,11840,11912,11984,12056,12128,12195,12263,12331,12390,12453,12517,12607,12698,12758,12824,12891,12957,13027,13091,13144,13211,13272,13339,13452,13510,13573,13638,13703,13778,13851,13923,13972,14033,14094,14155,14217,14281,14345,14409,14474,14537,14597,14658,14724,14783,14843,14905,14976,15036,15104,15678,15765,18097,18184,18272,18354,18437,18527,18618,20304,20362,20407,20473,20537,20594,20651,20705,26823,26871,26920,26971,29392,30066,30115,30370,31641,32072,32134,32194,32251,33317,33387,33465,33519,33589,33674,33722,33768,33829,33892,33958,34022,34093,34156,34221,34285,34346,34407,34459,34532,34606,34675,34750,34824,34898,35039,35109,45301,46145,46235,46323,46419,46509,47091,47180,47427,47708,47960,48245,48638,49115,49337,49559,49835,50062,50292,50522,50752,50982,51209,51628,51854,52279,52509,52937,53156,53439,53647,53778,54005,54431,54656,55083,55304,55729,55849,56125,56426,56750,57041,57355,57492,57623,57728,57970,58137,58341,58549,58820,58932,59044,59149,59266,59480,59626,59766,59852,60200,60288,60534,60952,61201,61283,61381,61973,62073,62325,62749,63004,63098,63187,63424,65448,65690,65792,66045,68201,78733,80249,90880,92408,94165,94791,95211,96272,97537,97793,98029,98576,99070,99675,99873,100453,101017,101392,101510,102048,102205,102401,102674,102930,103100,103241,103305,103670,104037,104713,104977,105315,105668,105762,105948,106254,106516,106641,106768,107007,107218,107337,107530,107707,108162,108343,108465,108724,108837,109024,109126,109233,109362,109637,110145,110641,111518,111812,112382,112531,113263,113435,113519,113855,113947,114225,131607,136996,137058,137636,138220,138311,146280,146509,146669,146821,146992,147158,147327,147494,147657,147900,148070,148243,148414,148688,148887,149092,149422,149506,149602,149698,149796,149896,149998,150100,150202,150304,150406,150506,150602,150714,150843,150966,151097,151228,151326,151440,151534,151674,151808,151904,152016,152116,152232,152328,152440,152540,152680,152816,152980,153110,153268,153418,153559,153703,153838,153950,154100,154228,154356,154492,154624,154754,154884,154996,155136,156422,156566,156704,156770,156860,156936,157040,157130,157232,157340,157448,157548,157628,157720,157818,157928,158006,158112,158204,158308,158418,158540,158703,158860,159024,159124,159214,159324,159414,159655,159749,159855,159947,160047,160159,160273,160389,160505,160599,160713,160825,160927,161047,161169,161251,161355,161475,161601,161699,161793,161881,161993,162109,162231,162343,162518,162634,162720,162812,162924,163048,163115,163241,163309,163437,163581,163709,163778,163873,163988,164101,164200,164309,164420,164531,164632,164737,164837,164967,165058,165181,165275,165387,165473,165577,165673,165761,165879,165983,166087,166213,166301,166409,166509,166599,166709,166793,166895,166979,167033,167097,167203,167289,167399,167483,167603,170358,170476,170591,170671,171032,171265,172412,174643,176004,176392,179167,189071,189499,191761,193638,199138,199400,199600,200311,204589,205195,209755,209906,214578,216501,217265,221860,223702,225833,226173,227484,227687"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e754f461ad8f7a511fa7fc6c4385c693\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,15,16,17,18,19,20,21,22,23,24,25,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,173,261,347,428,512,581,646,729,835,921,1041,1095,1164,1225,1294,1383,1478,1552,1649,1742,1840,1989,2080,2168,2264,2362,2426,2494,2581,2675,2742,2814,2886,2987,3096,3172,3241,3289,3355,3419,3493,3550,3607,3679,3729,3783,3854,3925,3995,4064,4122,4198,4269,4343,4429,4479,4549,4614,5329", "endLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,73,76", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "168,256,342,423,507,576,641,724,830,916,1036,1090,1159,1220,1289,1378,1473,1547,1644,1737,1835,1984,2075,2163,2259,2357,2421,2489,2576,2670,2737,2809,2881,2982,3091,3167,3236,3284,3350,3414,3488,3545,3602,3674,3724,3778,3849,3920,3990,4059,4117,4193,4264,4338,4424,4474,4544,4609,5324,5477"}, "to": {"startLines": "535,645,646,647,648,649,650,651,652,653,654,657,658,659,660,661,662,663,664,665,666,667,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,1820,1830", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "32412,40408,40496,40582,40663,40747,40816,40881,40964,41070,41156,41276,41330,41399,41460,41529,41618,41713,41787,41884,41977,42075,42224,42315,42403,42499,42597,42661,42729,42816,42910,42977,43049,43121,43222,43331,43407,43476,43524,43590,43654,43728,43785,43842,43914,43964,44018,44089,44160,44230,44299,44357,44433,44504,44578,44664,44714,44784,115780,116495", "endLines": "535,645,646,647,648,649,650,651,652,653,656,657,658,659,660,661,662,663,664,665,666,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,1829,1832", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "32480,40491,40577,40658,40742,40811,40876,40959,41065,41151,41271,41325,41394,41455,41524,41613,41708,41782,41879,41972,42070,42219,42310,42398,42494,42592,42656,42724,42811,42905,42972,43044,43116,43217,43326,43402,43471,43519,43585,43649,43723,43780,43837,43909,43959,44013,44084,44155,44225,44294,44352,44428,44499,44573,44659,44709,44779,44844,116490,116643"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6e4d5a7b637c696b107f8ca7a1e781c7\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}, "to": {"startLines": "476", "startColumns": "4", "startOffsets": "29246", "endColumns": "65", "endOffsets": "29307"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\464fb997f86c74cbb89077e01e66c511\\transformed\\activity-1.10.1\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "493,524", "startColumns": "4,4", "startOffsets": "30120,31732", "endColumns": "41,59", "endOffsets": "30157,31787"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\54c8e97fceebf05a01c2f3ccec507e1e\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "526", "startColumns": "4", "startOffsets": "31846", "endColumns": "49", "endOffsets": "31891"}}, {"source": "E:\\elmarjo\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1", "startColumns": "4", "startOffsets": "16", "endColumns": "44", "endOffsets": "56"}, "to": {"startLines": "575", "startColumns": "4", "startOffsets": "35197", "endColumns": "44", "endOffsets": "35237"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d0e5df3da8374bed71d9993cb807cb2d\\transformed\\core-1.16.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,100,101,105,106,107,108,114,124,159,180,213", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4484,4542,4603,4666,4723,4774,4832,4882,4943,5000,5066,5100,5135,5170,5240,5307,5379,5448,5517,5591,5663,5751,5822,5939,6140,6250,6451,6580,6652,6719,6922,7223,9029,9710,10392", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,99,100,104,105,106,107,113,123,158,179,212,218", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4479,4537,4598,4661,4718,4769,4827,4877,4938,4995,5061,5095,5130,5165,5235,5302,5374,5443,5512,5586,5658,5746,5817,5934,6135,6245,6446,6575,6647,6714,6917,7218,9024,9705,10387,10554"}, "to": {"startLines": "35,102,103,117,118,149,150,257,258,259,260,261,262,263,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,482,483,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,538,576,577,578,579,580,581,582,719,2428,2429,2434,2437,2442,2587,2588,3250,3267,3363,3398,3428,3461", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1449,4695,4767,5857,5922,7828,7897,15109,15179,15247,15319,15389,15450,15524,18846,18907,18968,19030,19094,19156,19217,19285,19385,19445,19511,19584,19653,19710,19762,24648,24720,24796,24861,24920,24979,25039,25099,25159,25219,25279,25339,25399,25459,25519,25579,25638,25698,25758,25818,25878,25938,25998,26058,26118,26178,26238,26297,26357,26417,26476,26535,26594,26653,26712,29572,29607,30375,30430,30493,30548,30606,30662,30720,30781,30844,30901,30952,31010,31060,31121,31178,31244,31278,31313,32611,35242,35309,35381,35450,35519,35593,35665,45480,155141,155258,155525,155818,156085,167608,167680,189504,190108,193792,195598,196598,197280", "endLines": "35,102,103,117,118,149,150,257,258,259,260,261,262,263,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,482,483,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,538,576,577,578,579,580,581,582,719,2428,2432,2434,2440,2442,2587,2588,3255,3276,3397,3418,3460,3466", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "1504,4762,4850,5917,5983,7892,7955,15174,15242,15314,15384,15445,15519,15592,18902,18963,19025,19089,19151,19212,19280,19380,19440,19506,19579,19648,19705,19757,19819,24715,24791,24856,24915,24974,25034,25094,25154,25214,25274,25334,25394,25454,25514,25574,25633,25693,25753,25813,25873,25933,25993,26053,26113,26173,26233,26292,26352,26412,26471,26530,26589,26648,26707,26766,29602,29637,30425,30488,30543,30601,30657,30715,30776,30839,30896,30947,31005,31055,31116,31173,31239,31273,31308,31343,32676,35304,35376,35445,35514,35588,35660,35748,45546,155253,155454,155630,156014,156209,167675,167742,189702,190404,195593,196274,197275,197442"}}, {"source": "E:\\elmarjo\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "56", "endColumns": "84", "endOffsets": "136"}, "to": {"startLines": "2479", "startColumns": "4", "startOffsets": "158865", "endColumns": "83", "endOffsets": "158944"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c19c93bf4b3ae41606569ce25471910a\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "480,487", "startColumns": "4,4", "startOffsets": "29452,29800", "endColumns": "53,66", "endOffsets": "29501,29862"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\649d543945bc612316d954c1790bc5ce\\transformed\\transition-1.4.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,95,142,185,240,287,341,393,442,503", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "90,137,180,235,282,336,388,437,498,548"}, "to": {"startLines": "474,475,486,494,495,516,517,518,519,520", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "29159,29199,29757,30162,30217,31348,31402,31454,31503,31564", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "29194,29241,29795,30212,30259,31397,31449,31498,31559,31609"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5510098f7f655104979ce51ebf04d4e9\\transformed\\media3-exoplayer-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,187,252,316,393,458,548,632", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "120,182,247,311,388,453,543,627,696"}, "to": {"startLines": "615,616,617,618,619,620,621,622,623", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "38267,38337,38399,38464,38528,38605,38670,38760,38844", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "38332,38394,38459,38523,38600,38665,38755,38839,38908"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\32ffae0c59a3b7f539124213f72623ec\\transformed\\media3-ui-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2,11,12,13,14,19,20,21,25,26,27,28,29,30,31,32,33,34,35,40,47,48,49,50,51,52,57,58,59,60,61,62,63,64,65,66,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,213,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,266,270,274,278,282,286,290,294,295,301,312,316,320,324,328,332,336,340,344,348,352,356,369,374,379,384,397,405,415,419,423,427,430,446,472,501", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,385,439,493,656,702,751,877,926,975,1034,1088,1140,1190,1255,1312,1359,1414,1562,1800,1849,1910,1970,2026,2086,2256,2316,2369,2426,2481,2537,2594,2643,2694,2753,3040,3105,3163,3212,3260,3311,3368,3425,3487,3554,3625,3697,3741,3798,3854,3917,3990,4060,4119,4176,4223,4278,4323,4372,4427,4481,4531,4582,4636,4695,4745,4803,4859,4912,4975,5040,5103,5155,5215,5279,5345,5403,5475,5536,5606,5676,5741,5806,5877,5972,6077,6180,6261,6344,6425,6514,6607,6700,6793,6878,6973,7066,7143,7235,7313,7393,7471,7557,7639,7732,7810,7901,7982,8071,8174,8275,8359,8455,8552,8647,8740,8832,8925,9018,9111,9194,9281,9376,9469,9550,9645,9738,9815,9859,9900,9945,9993,10037,10080,10129,10176,10220,10276,10329,10371,10418,10466,10526,10564,10614,10658,10708,10760,10798,10845,10892,10933,10972,11010,11054,11102,11144,11182,11224,11278,11325,11362,11411,11453,11494,11535,11577,11620,11658,11694,11772,11850,12147,12417,12499,12581,12723,12801,12888,12973,13040,13103,13195,13287,13352,13415,13477,13548,13658,13769,13879,13946,14026,14097,14164,14249,14334,14397,14485,14549,14691,14791,14839,14982,15045,15107,15172,15243,15301,15359,15425,15489,15555,15607,15669,15745,15821,15875,16154,16385,16595,16808,17018,17240,17456,17660,17698,18052,18839,19080,19320,19577,19830,20083,20318,20565,20804,21048,21269,21464,22136,22427,22723,23026,23692,24226,24700,24911,25111,25287,25395,25971,26916,27966", "endLines": "10,11,12,13,18,19,20,24,25,26,27,28,29,30,31,32,33,34,39,46,47,48,49,50,51,56,57,58,59,60,61,62,63,64,65,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,212,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,265,269,273,277,281,285,289,293,294,300,311,315,319,323,327,331,335,339,343,347,351,355,368,373,378,383,396,404,414,418,422,426,429,445,471,500,540", "endColumns": "17,49,53,53,9,45,48,9,48,48,58,53,51,49,64,56,46,54,9,9,48,60,59,55,59,9,59,52,56,54,55,56,48,50,58,9,64,57,48,47,50,56,56,61,66,70,71,43,56,55,62,72,69,58,56,46,54,44,48,54,53,49,50,53,58,49,57,55,52,62,64,62,51,59,63,65,57,71,60,69,69,64,64,70,94,104,102,80,82,80,88,92,92,92,84,94,92,76,91,77,79,77,85,81,92,77,90,80,88,102,100,83,95,96,94,92,91,92,92,92,82,86,94,92,80,94,92,76,43,40,44,47,43,42,48,46,43,55,52,41,46,47,59,37,49,43,49,51,37,46,46,40,38,37,43,47,41,37,41,53,46,36,48,41,40,40,41,42,37,35,77,77,12,12,81,81,141,77,86,84,66,62,91,91,64,62,61,70,109,110,109,66,79,70,66,84,84,62,87,63,141,99,47,142,62,61,64,70,57,57,65,63,65,51,61,75,75,53,10,10,10,10,10,10,10,10,37,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,22,22,22,22,22", "endOffsets": "330,380,434,488,651,697,746,872,921,970,1029,1083,1135,1185,1250,1307,1354,1409,1557,1795,1844,1905,1965,2021,2081,2251,2311,2364,2421,2476,2532,2589,2638,2689,2748,3035,3100,3158,3207,3255,3306,3363,3420,3482,3549,3620,3692,3736,3793,3849,3912,3985,4055,4114,4171,4218,4273,4318,4367,4422,4476,4526,4577,4631,4690,4740,4798,4854,4907,4970,5035,5098,5150,5210,5274,5340,5398,5470,5531,5601,5671,5736,5801,5872,5967,6072,6175,6256,6339,6420,6509,6602,6695,6788,6873,6968,7061,7138,7230,7308,7388,7466,7552,7634,7727,7805,7896,7977,8066,8169,8270,8354,8450,8547,8642,8735,8827,8920,9013,9106,9189,9276,9371,9464,9545,9640,9733,9810,9854,9895,9940,9988,10032,10075,10124,10171,10215,10271,10324,10366,10413,10461,10521,10559,10609,10653,10703,10755,10793,10840,10887,10928,10967,11005,11049,11097,11139,11177,11219,11273,11320,11357,11406,11448,11489,11530,11572,11615,11653,11689,11767,11845,12142,12412,12494,12576,12718,12796,12883,12968,13035,13098,13190,13282,13347,13410,13472,13543,13653,13764,13874,13941,14021,14092,14159,14244,14329,14392,14480,14544,14686,14786,14834,14977,15040,15102,15167,15238,15296,15354,15420,15484,15550,15602,15664,15740,15816,15870,16149,16380,16590,16803,17013,17235,17451,17655,17693,18047,18834,19075,19315,19572,19825,20078,20313,20560,20799,21043,21264,21459,22131,22422,22718,23021,23687,24221,24695,24906,25106,25282,25390,25966,26911,27961,29319"}, "to": {"startLines": "2,11,12,13,14,19,20,21,25,26,27,28,31,32,34,36,37,38,40,45,52,53,54,55,56,57,62,63,64,65,66,67,68,69,70,79,86,88,89,90,91,125,126,127,128,129,130,131,132,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,533,534,539,543,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,1833,1838,1842,1846,1850,1854,1858,1862,1866,1867,1873,1884,1888,1892,1896,1900,1904,1908,1912,1916,1920,1924,1928,1941,1946,1951,1956,1969,1977,1987,1991,1995,3238,3305,3467,3751,3780", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,430,484,538,701,747,796,922,971,1020,1079,1233,1285,1384,1509,1566,1613,1724,1872,2110,2159,2220,2280,2336,2396,2566,2626,2679,2736,2791,2847,2904,2953,3004,3399,3686,3792,3850,3899,3947,6389,6446,6503,6565,6632,6703,6775,6819,15770,15826,15889,15962,16032,16091,16148,16195,16250,16295,16344,16399,16453,16503,16554,16608,16667,16717,16775,16831,16884,16947,17012,17075,17127,17187,17251,17317,17375,17447,17508,17578,17648,17713,17778,20710,20805,20910,21013,21094,21177,21258,21347,21440,21533,21626,21711,21806,21899,21976,22068,22146,22226,22304,22390,22472,22565,22643,22734,22815,22904,23007,23108,23192,23288,23385,23480,23573,23665,23758,23851,23944,24027,24114,24209,24302,24383,24478,24571,27223,27267,27308,27353,27401,27445,27488,27537,27584,27628,27684,27737,27779,27826,27874,27934,27972,28022,28066,28116,28168,28206,28253,28300,28341,28380,28418,28462,28510,28552,28590,28632,28686,28733,28770,28819,28861,28902,28943,28985,29028,29066,32256,32334,32681,32978,36135,36217,36299,36441,36519,36606,36691,36758,36821,36913,37005,37070,37133,37195,37266,37376,37487,37597,37664,37744,37815,37882,37967,38052,38115,38203,38913,39055,39155,39203,39346,39409,39471,39536,39607,39665,39723,39789,39853,39919,39971,40033,40109,40185,116648,116927,117158,117368,117581,117791,118013,118229,118433,118471,118825,119612,119853,120093,120350,120603,120856,121091,121338,121577,121821,122042,122237,122909,123200,123496,123799,124465,124999,125473,125684,125884,189076,191766,197447,207123,208173", "endLines": "10,11,12,13,18,19,20,24,25,26,27,28,31,32,34,36,37,38,44,51,52,53,54,55,56,61,62,63,64,65,66,67,68,69,70,85,86,88,89,90,91,125,126,127,128,129,130,131,132,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,533,534,542,546,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,1837,1841,1845,1849,1853,1857,1861,1865,1866,1872,1883,1887,1891,1895,1899,1903,1907,1911,1915,1919,1923,1927,1940,1945,1950,1955,1968,1976,1986,1990,1994,1998,3240,3320,3492,3779,3819", "endColumns": "17,49,53,53,9,45,48,9,48,48,58,53,51,49,64,56,46,54,9,9,48,60,59,55,59,9,59,52,56,54,55,56,48,50,58,9,64,57,48,47,50,56,56,61,66,70,71,43,56,55,62,72,69,58,56,46,54,44,48,54,53,49,50,53,58,49,57,55,52,62,64,62,51,59,63,65,57,71,60,69,69,64,64,70,94,104,102,80,82,80,88,92,92,92,84,94,92,76,91,77,79,77,85,81,92,77,90,80,88,102,100,83,95,96,94,92,91,92,92,92,82,86,94,92,80,94,92,76,43,40,44,47,43,42,48,46,43,55,52,41,46,47,59,37,49,43,49,51,37,46,46,40,38,37,43,47,41,37,41,53,46,36,48,41,40,40,41,42,37,35,77,77,12,12,81,81,141,77,86,84,66,62,91,91,64,62,61,70,109,110,109,66,79,70,66,84,84,62,87,63,141,99,47,142,62,61,64,70,57,57,65,63,65,51,61,75,75,53,10,10,10,10,10,10,10,10,37,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,22,22,22,22,22", "endOffsets": "375,425,479,533,696,742,791,917,966,1015,1074,1128,1280,1330,1444,1561,1608,1663,1867,2105,2154,2215,2275,2331,2391,2561,2621,2674,2731,2786,2842,2899,2948,2999,3058,3681,3746,3845,3894,3942,3993,6441,6498,6560,6627,6698,6770,6814,6871,15821,15884,15957,16027,16086,16143,16190,16245,16290,16339,16394,16448,16498,16549,16603,16662,16712,16770,16826,16879,16942,17007,17070,17122,17182,17246,17312,17370,17442,17503,17573,17643,17708,17773,17844,20800,20905,21008,21089,21172,21253,21342,21435,21528,21621,21706,21801,21894,21971,22063,22141,22221,22299,22385,22467,22560,22638,22729,22810,22899,23002,23103,23187,23283,23380,23475,23568,23660,23753,23846,23939,24022,24109,24204,24297,24378,24473,24566,24643,27262,27303,27348,27396,27440,27483,27532,27579,27623,27679,27732,27774,27821,27869,27929,27967,28017,28061,28111,28163,28201,28248,28295,28336,28375,28413,28457,28505,28547,28585,28627,28681,28728,28765,28814,28856,28897,28938,28980,29023,29061,29097,32329,32407,32973,33243,36212,36294,36436,36514,36601,36686,36753,36816,36908,37000,37065,37128,37190,37261,37371,37482,37592,37659,37739,37810,37877,37962,38047,38110,38198,38262,39050,39150,39198,39341,39404,39466,39531,39602,39660,39718,39784,39848,39914,39966,40028,40104,40180,40234,116922,117153,117363,117576,117786,118008,118224,118428,118466,118820,119607,119848,120088,120345,120598,120851,121086,121333,121572,121816,122037,122232,122904,123195,123491,123794,124460,124994,125468,125679,125879,126055,189179,192337,198387,208168,209526"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5ed89daf239d020270328bf0b2917631\\transformed\\savedstate-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "525", "startColumns": "4", "startOffsets": "31792", "endColumns": "53", "endOffsets": "31841"}}]}]}